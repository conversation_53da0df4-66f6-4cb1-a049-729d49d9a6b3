import React from "react";
import { motion } from "framer-motion";
import {
  <PERSON><PERSON><PERSON>t<PERSON><PERSON>gle,
  LuRefreshCw,
  LuWifi,
  LuServer,
  LuShield,
} from "react-icons/lu";
import Card from "@/components/ui/card";
import { Button } from "@/components/ui/button";

interface ErrorDisplayProps {
  error: any;
  onRetry?: () => void;
  title?: string;
  className?: string;
}

const ErrorDisplay: React.FC<ErrorDisplayProps> = ({
  error,
  onRetry,
  title = "Error",
  className = "",
}) => {
  // Extract error information
  const errorCode = error?.code || "unknown_error";
  const errorMessage = error?.message || "An unexpected error occurred";
  const severity = error?.severity || "medium";
  const shouldRetry = error?.shouldRetry !== false;

  // Get appropriate icon based on error type
  const getErrorIcon = () => {
    switch (errorCode) {
      case "network_error":
      case "timeout":
        return <LuWifi className="w-12 h-12 text-orange-500" />;
      case "server_error":
      case "bad_gateway":
      case "service_unavailable":
      case "gateway_timeout":
        return <LuServer className="w-12 h-12 text-red-500" />;
      case "unauthorized":
      case "forbidden":
        return <LuShield className="w-12 h-12 text-yellow-500" />;
      default:
        return <LuAlertTriangle className="w-12 h-12 text-gray-500" />;
    }
  };

  // Get error color based on severity
  const getErrorColor = () => {
    switch (severity) {
      case "critical":
        return "text-red-600";
      case "high":
        return "text-red-500";
      case "medium":
        return "text-orange-500";
      case "low":
        return "text-yellow-500";
      default:
        return "text-gray-500";
    }
  };

  // Get user-friendly error title
  const getErrorTitle = () => {
    switch (errorCode) {
      case "network_error":
        return "Connection Problem";
      case "timeout":
        return "Request Timeout";
      case "server_error":
        return "Something Went Wrong";
      case "bad_gateway":
      case "service_unavailable":
        return "Service Unavailable";
      case "gateway_timeout":
        return "Gateway Timeout";
      case "unauthorized":
        return "Authentication Required";
      case "forbidden":
        return "Access Denied";
      case "not_found":
        return "Data Not Found";
      case "rate_limited":
        return "Too Many Requests";
      default:
        return "Something Went Wrong";
    }
  };

  return (
    <Card className={`p-8 ${className}`}>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        className="flex flex-col items-center justify-center text-center space-y-4"
      >
        {/* Error Icon */}
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.1, type: "spring", stiffness: 200 }}
        >
          {getErrorIcon()}
        </motion.div>

        {/* Error Title */}
        <div className="space-y-2">
          <h3 className={`text-xl font-semibold ${getErrorColor()}`}>
            {getErrorTitle()}
          </h3>
          <p className="text-gray-600 max-w-md">{errorMessage}</p>
        </div>

        {/* Error Code (for debugging) */}
        {process.env.NODE_ENV === "development" && (
          <div className="text-xs text-gray-400 font-mono bg-gray-100 px-2 py-1 rounded">
            Error Code: {errorCode}
          </div>
        )}

        {/* Retry Button - Always show for manual retry */}
        {onRetry && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.3 }}
          >
            <Button
              onClick={onRetry}
              variant={severity === "critical" ? "default" : "outline"}
              className="flex items-center space-x-2"
            >
              <LuRefreshCw className="w-4 h-4" />
              <span>Try Again</span>
            </Button>
          </motion.div>
        )}

        {/* Additional Help Text */}
        <div className="text-sm text-gray-500 max-w-md">
          {errorCode === "network_error" && (
            <p>Please check your internet connection and try again.</p>
          )}
          {errorCode === "server_error" && (
            <p>
              We're experiencing technical difficulties. Please try again in a
              few minutes.
            </p>
          )}
          {errorCode === "unauthorized" && (
            <p>Please log in to access this data.</p>
          )}
          {errorCode === "forbidden" && (
            <p>You may need additional permissions to view this data.</p>
          )}
          {errorCode === "rate_limited" && (
            <p>Please wait a moment before making another request.</p>
          )}
          {(errorCode === "service_unavailable" ||
            errorCode === "bad_gateway") && (
            <p>
              The service is temporarily unavailable. Please try again later.
            </p>
          )}
        </div>
      </motion.div>
    </Card>
  );
};

export default ErrorDisplay;
