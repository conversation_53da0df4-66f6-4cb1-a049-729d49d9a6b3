import React from "react";
import { motion } from "framer-motion";
import LineChartCard from "../../../_components/LineChartCard";
import {
  useLineChartDataStore,
  useChartPopupStore,
} from "@/store/useChartPopupStore";
import type { ChartDataPoint } from "../AudienceOverview.types";

interface EventCountChartProps {
  title: string;
  bigNumber: string;
  smallNumber: string;
  data: ChartDataPoint[];
  hasComparison?: boolean;
}

const EventCountChart: React.FC<EventCountChartProps> = ({
  title,
  bigNumber,
  smallNumber,
  data,
  hasComparison,
}) => {
  const setChartData = useLineChartDataStore((setData) => setData.setChartData);
  const { show } = useChartPopupStore();

  const handleSetChartData = () => {
    setChartData({
      title,
      bigNumber,
      smallNumber,
      data,
      hasComparison,
    });
    show();
  };

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.5 }}
      className="w-full lg:col-span-2"
    >
      <LineChartCard
        title={title}
        className="w-full cursor-pointer"
        bigNumber={bigNumber}
        smallNumber={smallNumber}
        data={data}
        onClick={handleSetChartData}
      />
    </motion.div>
  );
};

export default EventCountChart;
