"use client";
import React, { useEffect, useRef, useState } from "react";

/* =============================== COMPONENTS =============================== */
import Card from "@/components/ui/card";
import Title from "@/components/ui/Title";
import DateRange from "../../../../../_components/date-range/DateRange";
import CardTab from "@/components/ui/card-tab/CardTab";
import GSCLineChart from "../../../../_components/line-chart/LineChart";
import ProgressBar from "../../../../../overview/(overview)/users-overview/_components/ProgressBar";
import useAudience from "../../../audience/Audience.hook";
import NoData from "../../../../_components/NoData";
import ErrorDisplay from "../../../../_components/ErrorDisplay";
import Dropdown from "@/components/ui/Dropdown";
import { useAppThemeColor } from "@/hooks/useSidebarThemeColor";
import useChartAndBarsData from "./ChartAndBars.hook";

/* ============================== FRAMER MOTION ============================= */
import { motion } from "framer-motion";

/* ================================= LODASH ================================= */
import isEqual from "lodash.isequal";

/* ================================== TYPES ================================= */
import type { CardTabType } from "@/app/(dashboard)/project/[projectId]/analytics-traffics/types/AnalyticsTraffics.types";
import LineChartSkeleton from "@/app/(dashboard)/project/[projectId]/analytics-traffics/_components/line-chart-skeleton/LineChartSkeleton";

/* ========================================================================== */
const ChartAndBars = () => {
  /* ========================================================================== */
  /*                                  CONSTANTS                                 */
  /* ========================================================================== */
  const [activeTab, setActiveTab] = useState("");
  const { themeColor } = useAppThemeColor();
  const filters = ["Age", "Countries", "Cities", "Language", "Gender"];
  const [activeTabFilter, setActiveTabFilter] = useState(filters[0]);

  const domesticInfoFilters = ["Demographic info ", "Tech info "];
  const [activeDomesticFilter, setActiveDomesticFilter] = useState(
    domesticInfoFilters[0]
  );

  // Use the same hook as Audience component for chart data
  const { data, isError, error, isLoading, isPending, refetch } =
    useAudience(activeTab);

  // Use custom hook for progress bar data based on selected filter
  const {
    progressBarData,
    isLoading: isProgressBarLoading,
    error: progressBarError,
    hasData: hasProgressBarData,
  } = useChartAndBarsData(activeTabFilter);

  const prevCardTabsRef = useRef<CardTabType[] | null>(data?.cardTabs ?? null);
  const [cardsDataChanged, setCardsDataChanged] = useState(false);

  useEffect(() => {
    if (data?.cardTabs && !isEqual(prevCardTabsRef.current, data.cardTabs)) {
      prevCardTabsRef.current = data.cardTabs;
      setCardsDataChanged(true);
    } else {
      setCardsDataChanged(false);
    }
  }, [data?.cardTabs]);

  useEffect(() => {
    if (data && !activeTab) setActiveTab(data?.cardTabs[0]?.title);
  }, [data, activeTab]);

  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */

  // Handle error state first - but only show error display for critical errors
  if (isError) {
    const errorCode = (error as any)?.code;
    const errorStatus =
      (error as any)?.response?.status || (error as any)?.status;

    // Don't show error display for 404 (not found) - just show no data instead
    if (errorCode === "not_found" || errorStatus === 404) {
      return <NoData title="About Users" />;
    }

    // Show error display for other errors (500, network issues, auth, etc.)
    return (
      <ErrorDisplay
        error={error}
        onRetry={() => refetch()}
        title="About Users Data"
        className="min-h-[400px]"
      />
    );
  }

  // Handle no data state (when not loading and no error)
  if (!data && !isLoading && !isPending) {
    return <NoData title="About Users" />;
  }

  // Main render - loading or data available
  return (
    <Card className="space-y-4">
      <div className="space-y-2">
        <div className="flex w-full justify-between items-center">
          <Title>About Users</Title>
          <Dropdown>
            <Dropdown.Button className="">
              {activeDomesticFilter}
            </Dropdown.Button>
            <Dropdown.Options>
              {domesticInfoFilters.map((filter, index) => (
                <Dropdown.Option
                  key={index}
                  onClick={() => setActiveDomesticFilter(filter)}
                >
                  {filter}
                </Dropdown.Option>
              ))}
            </Dropdown.Options>
          </Dropdown>
        </div>
        <DateRange />
      </div>

      {/* Card Tabs Section - Using Audience data */}
      <div className="flex overflow-x-auto max-w-full gap-1.5 px-1 pb-2 whitespace-nowrap">
        {cardsDataChanged || isLoading || isPending ? (
          <motion.div
            className="flex overflow-x-auto max-w-full gap-1.5 px-1 pb-2 whitespace-nowrap"
            key={"loading"}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.5 }}
          >
            {Array.from({ length: 7 }).map((_, i) => (
              <CardTab key={i} isLoading />
            ))}
          </motion.div>
        ) : (
          data?.cardTabs && (
            <motion.div
              className="flex overflow-x-auto max-w-full gap-1.5 px-1 pb-2 whitespace-nowrap"
              key={"data"}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.5 }}
            >
              {data.cardTabs.map(
                (
                  {
                    title,
                    changeValue,
                    value,
                  }: { title: string; changeValue: string; value: string },
                  index: number
                ) => (
                  <CardTab
                    key={index}
                    title={title}
                    value={value}
                    changeValue={changeValue}
                    className={`border-2 ${
                      activeTab === title
                        ? "border-primary"
                        : "border-transparent"
                    }`}
                    style={
                      activeTab === title
                        ? { borderColor: themeColor }
                        : { borderColor: "transparent" }
                    }
                    onSelect={() => setActiveTab(title)}
                  />
                )
              )}
            </motion.div>
          )
        )}
      </div>

      <div className="w-full flex justify-end">
        <Dropdown>
          <Dropdown.Button className="min-w-24 text-sm">
            {activeTabFilter}
          </Dropdown.Button>
          <Dropdown.Options>
            {filters.map((filter, index) => (
              <Dropdown.Option
                key={index}
                onClick={() => {
                  setActiveTabFilter(filter);
                }}
              >
                {filter}
              </Dropdown.Option>
            ))}
          </Dropdown.Options>
        </Dropdown>
      </div>

      {/* Chart and Progress Bars Section */}
      <div className="flex flex-col-reverse lg:flex-row items-center lg:items-start gap-y-16 min-h-[310px]">
        {/* Chart Section - Using Audience chart data */}
        <div className="w-full min-h-[310px] flex items-center">
          {isLoading || isPending ? (
            <motion.div
              key={"loading"}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.5 }}
              className="w-full"
            >
              <LineChartSkeleton />
            </motion.div>
          ) : (
            data && (
              <motion.div
                key={"data"}
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.5 }}
                className="w-full"
              >
                <GSCLineChart
                  lineChartData={data.lineChartData}
                  colors={data.colors}
                  selectedLines={data.selectedLines}
                  cardsData={data.cardsData}
                />
              </motion.div>
            )
          )}
        </div>

        {/* Progress Bars Section - Real API data for Age filter, mock for others */}
        <div className="w-full lg:w-[40%] h-[310px] max-h-[310px] overflow-y-auto">
          <div className="space-y-2 pr-2">
            {isProgressBarLoading ? (
              // Show loading skeleton for progress bars
              Array.from({ length: 5 }).map((_, index) => (
                <ProgressBar
                  key={index}
                  isLoading={true}
                  percentage={0}
                  title=""
                  color=""
                />
              ))
            ) : progressBarError ? (
              // Show error state for progress bars
              <div className="text-center text-red-500 py-4">
                <p>Error loading {activeTabFilter.toLowerCase()} data</p>
              </div>
            ) : hasProgressBarData ? (
              // Show actual progress bar data
              progressBarData.map(({ title, percentage }, index) => (
                <ProgressBar
                  key={index}
                  isLoading={false}
                  percentage={percentage}
                  title={title}
                  color={index === 0 ? "bg-[#F8BD00]" : ""}
                />
              ))
            ) : (
              // Show no data message
              <div className="text-center text-gray-500 py-4">
                <p>No {activeTabFilter.toLowerCase()} data available</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </Card>
  );
};

export default ChartAndBars;
