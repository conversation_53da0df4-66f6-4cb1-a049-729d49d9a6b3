/**
 * Error handling utilities
 * This module provides functions to handle errors consistently across the application
 */

// Define error types
export enum ErrorType {
  AUTHENTICATION = "authentication",
  VALIDATION = "validation",
  SERVER = "server",
  NETWORK = "network",
  UNKNOWN = "unknown",
}

// Define error severity levels
export enum ErrorSeverity {
  INFO = "info",
  WARNING = "warning",
  ERROR = "error",
  CRITICAL = "critical",
}

// Define error interface
export interface AppError {
  type: ErrorType;
  message: string;
  severity: ErrorSeverity;
  details?: any;
  fieldErrors?: Record<string, string[]>;
}

// Enhanced error type for better error handling
export interface AuthError {
  code: string;
  message: string;
  field?: string;
  details?: any;
}

/**
 * Format authentication errors
 * @param error The error object
 * @returns Formatted error object
 */
export function formatAuthError(error: any): AppError {
  // Default error
  const defaultError: AppError = {
    type: ErrorType.AUTHENTICATION,
    message: "Authentication failed. Please try again.",
    severity: ErrorSeverity.ERROR,
  };

  // If the error is already formatted, return it
  if (error && error.type === ErrorType.AUTHENTICATION) {
    return error;
  }

  // If the error is an API response
  if (error && error.statusCode) {
    // Handle field errors
    if (error.fieldErrors) {
      return {
        type: ErrorType.VALIDATION,
        message: "Please correct the errors below.",
        severity: ErrorSeverity.WARNING,
        fieldErrors: error.fieldErrors,
      };
    }

    // Handle error message
    if (error.error) {
      return {
        type: ErrorType.AUTHENTICATION,
        message: error.error,
        severity: ErrorSeverity.ERROR,
        details: error,
      };
    }
  }

  // If the error is a string
  if (typeof error === "string") {
    return {
      type: ErrorType.AUTHENTICATION,
      message: error,
      severity: ErrorSeverity.ERROR,
    };
  }

  // If the error is an Error object
  if (error instanceof Error) {
    return {
      type: ErrorType.AUTHENTICATION,
      message: error.message,
      severity: ErrorSeverity.ERROR,
      details: error,
    };
  }

  // Return default error
  return defaultError;
}

/**
 * Format validation errors
 * @param error The error object
 * @returns Formatted error object
 */
export function formatValidationError(error: any): AppError {
  // Default error
  const defaultError: AppError = {
    type: ErrorType.VALIDATION,
    message: "Validation failed. Please check your input.",
    severity: ErrorSeverity.WARNING,
  };

  // If the error is already formatted, return it
  if (error && error.type === ErrorType.VALIDATION) {
    return error;
  }

  // If the error has field errors
  if (error && error.fieldErrors) {
    return {
      type: ErrorType.VALIDATION,
      message: "Please correct the errors below.",
      severity: ErrorSeverity.WARNING,
      fieldErrors: error.fieldErrors,
    };
  }

  // Return default error
  return defaultError;
}

/**
 * Format server errors
 * @param error The error object
 * @returns Formatted error object
 */
export function formatServerError(error: any): AppError {
  // Default error
  const defaultError: AppError = {
    type: ErrorType.SERVER,
    message: "Server error. Please try again later.",
    severity: ErrorSeverity.ERROR,
  };

  // If the error is already formatted, return it
  if (error && error.type === ErrorType.SERVER) {
    return error;
  }

  // If the error is an API response
  if (error && error.statusCode) {
    if (error.statusCode >= 500) {
      return {
        type: ErrorType.SERVER,
        message: error.error || "Server error. Please try again later.",
        severity: ErrorSeverity.ERROR,
        details: error,
      };
    }
  }

  // Return default error
  return defaultError;
}

/**
 * Format network errors
 * @param error The error object
 * @returns Formatted error object
 */
export function formatNetworkError(error: any): AppError {
  return {
    type: ErrorType.NETWORK,
    message: "Network error. Please check your connection and try again.",
    severity: ErrorSeverity.WARNING,
    details: error,
  };
}

/**
 * Format unknown errors
 * @param error The error object
 * @returns Formatted error object
 */
export function formatUnknownError(error: any): AppError {
  return {
    type: ErrorType.UNKNOWN,
    message: "An unexpected error occurred. Please try again.",
    severity: ErrorSeverity.ERROR,
    details: error,
  };
}

/**
 * Format any error
 * @param error The error object
 * @returns Formatted error object
 */
export function formatError(error: any): AppError {
  // If the error is already formatted, return it
  if (error && error.type && Object.values(ErrorType).includes(error.type)) {
    return error as AppError;
  }

  // If the error is an API response with a status code
  if (error && error.statusCode) {
    if (error.statusCode === 401) {
      return formatAuthError(error);
    } else if (error.statusCode === 400 || error.statusCode === 422) {
      return formatValidationError(error);
    } else if (error.statusCode >= 500) {
      return formatServerError(error);
    }
  }

  // If the error is a network error
  if (error && error.message && error.message.includes("Network")) {
    return formatNetworkError(error);
  }

  // Default to unknown error
  return formatUnknownError(error);
}

/**
 * Enhanced error result interface for better error handling
 */
export interface ErrorHandlerResult {
  userMessage: string;
  technicalMessage: string;
  shouldRetry: boolean;
  severity: "low" | "medium" | "high" | "critical";
  code: string;
}

/**
 * Enhanced API error processing with comprehensive HTTP status code handling
 * @param error The error object from an API call
 * @returns Enhanced error result with user-friendly messages and retry logic
 */
export function processApiErrorEnhanced(error: any): ErrorHandlerResult {
  const status = error?.response?.status || error?.status || 0;
  const serverMessage =
    error?.response?.data?.message ||
    error?.response?.data?.detail ||
    error?.message ||
    "Unknown error";
  const endpoint = error?.config?.url || "unknown endpoint";

  switch (status) {
    case 400:
      return {
        userMessage: "Invalid request. Please check your input and try again.",
        technicalMessage: `Bad Request (400): ${serverMessage} - Endpoint: ${endpoint}`,
        shouldRetry: false,
        severity: "medium",
        code: "bad_request",
      };

    case 401:
      return {
        userMessage: "Authentication required. Please log in to continue.",
        technicalMessage: `Unauthorized (401): ${serverMessage} - Endpoint: ${endpoint}`,
        shouldRetry: false,
        severity: "high",
        code: "unauthorized",
      };

    case 403:
      return {
        userMessage:
          "Access denied. You don't have permission to access this resource.",
        technicalMessage: `Forbidden (403): ${serverMessage} - Endpoint: ${endpoint}`,
        shouldRetry: false,
        severity: "high",
        code: "forbidden",
      };

    case 404:
      return {
        userMessage:
          "The requested data was not found. It may have been moved or deleted.",
        technicalMessage: `Not Found (404): ${serverMessage} - Endpoint: ${endpoint}`,
        shouldRetry: false,
        severity: "medium",
        code: "not_found",
      };

    case 408:
      return {
        userMessage: "Request timeout. Please try again.",
        technicalMessage: `Request Timeout (408): ${serverMessage} - Endpoint: ${endpoint}`,
        shouldRetry: true,
        severity: "medium",
        code: "timeout",
      };

    case 409:
      return {
        userMessage:
          "Conflict detected. The resource may have been modified by another user.",
        technicalMessage: `Conflict (409): ${serverMessage} - Endpoint: ${endpoint}`,
        shouldRetry: false,
        severity: "medium",
        code: "conflict",
      };

    case 422:
      return {
        userMessage:
          "Invalid data provided. Please check your input and try again.",
        technicalMessage: `Unprocessable Entity (422): ${serverMessage} - Endpoint: ${endpoint}`,
        shouldRetry: false,
        severity: "medium",
        code: "validation_error",
      };

    case 429:
      return {
        userMessage: "Too many requests. Please wait a moment and try again.",
        technicalMessage: `Rate Limited (429): ${serverMessage} - Endpoint: ${endpoint}`,
        shouldRetry: true,
        severity: "medium",
        code: "rate_limited",
      };

    case 500:
      return {
        userMessage: "Something went wrong on our end. Please try again later.",
        technicalMessage: `Internal Server Error (500): ${serverMessage} - Endpoint: ${endpoint}`,
        shouldRetry: false, // Don't auto-retry server errors
        severity: "critical",
        code: "server_error",
      };

    case 502:
      return {
        userMessage:
          "Service temporarily unavailable. Please try again in a few minutes.",
        technicalMessage: `Bad Gateway (502): ${serverMessage} - Endpoint: ${endpoint}`,
        shouldRetry: false, // Don't auto-retry, let user manually retry
        severity: "high",
        code: "bad_gateway",
      };

    case 503:
      return {
        userMessage:
          "Service is currently under maintenance. Please try again later.",
        technicalMessage: `Service Unavailable (503): ${serverMessage} - Endpoint: ${endpoint}`,
        shouldRetry: false, // Don't auto-retry, let user manually retry
        severity: "high",
        code: "service_unavailable",
      };

    case 504:
      return {
        userMessage:
          "Request timeout. The server took too long to respond. Please try again.",
        technicalMessage: `Gateway Timeout (504): ${serverMessage} - Endpoint: ${endpoint}`,
        shouldRetry: false, // Don't auto-retry, let user manually retry
        severity: "high",
        code: "gateway_timeout",
      };

    // Network errors (no status code)
    case 0:
      if (
        error?.code === "NETWORK_ERROR" ||
        error?.message?.includes("Network Error")
      ) {
        return {
          userMessage:
            "Network connection error. Please check your internet connection and try again.",
          technicalMessage: `Network Error: ${serverMessage} - Endpoint: ${endpoint}`,
          shouldRetry: true,
          severity: "high",
          code: "network_error",
        };
      }

      if (
        error?.code === "ECONNABORTED" ||
        error?.message?.includes("timeout")
      ) {
        return {
          userMessage: "Request timeout. Please try again.",
          technicalMessage: `Timeout Error: ${serverMessage} - Endpoint: ${endpoint}`,
          shouldRetry: true,
          severity: "medium",
          code: "timeout",
        };
      }

      return {
        userMessage: "An unexpected error occurred. Please try again.",
        technicalMessage: `Unknown Error: ${serverMessage} - Endpoint: ${endpoint}`,
        shouldRetry: true,
        severity: "medium",
        code: "unknown_error",
      };

    default:
      return {
        userMessage: "An unexpected error occurred. Please try again.",
        technicalMessage: `HTTP ${status}: ${serverMessage} - Endpoint: ${endpoint}`,
        shouldRetry: status >= 500,
        severity: status >= 500 ? "critical" : "medium",
        code: `http_${status}`,
      };
  }
}

/**
 * Logs errors based on severity level
 */
export const logError = (errorResult: ErrorHandlerResult, context?: string) => {
  const prefix = context ? `[${context}]` : "[API Error]";

  switch (errorResult.severity) {
    case "critical":
      console.error(`${prefix} CRITICAL:`, errorResult.technicalMessage);
      break;
    case "high":
      console.error(`${prefix} HIGH:`, errorResult.technicalMessage);
      break;
    case "medium":
      console.warn(`${prefix} MEDIUM:`, errorResult.technicalMessage);
      break;
    case "low":
      console.info(`${prefix} LOW:`, errorResult.technicalMessage);
      break;
  }
};

/**
 * Process API errors into a structured format (Legacy function - kept for backward compatibility)
 * @param error The error object from an API call
 * @returns Structured error object or array of error objects
 */
export function processApiError(error: any): AuthError | AuthError[] {
  // Default error
  const defaultError: AuthError = {
    code: "unknown_error",
    message: "An unexpected error occurred. Please try again.",
  };

  // If no error, return default
  if (!error) return defaultError;

  // If error is already in our format, return it
  if (error.code && error.message) {
    return error as AuthError;
  }

  // Handle Axios error responses
  if (error.response) {
    const { status, data } = error.response;

    // Handle different status codes
    switch (status) {
      case 400: // Bad Request
        // Check for field validation errors
        if (data.fieldErrors || data.errors) {
          const fieldErrors = data.fieldErrors || data.errors;
          const errors: AuthError[] = [];

          // Process each field error
          for (const [field, messages] of Object.entries(fieldErrors)) {
            if (Array.isArray(messages)) {
              messages.forEach((message) => {
                errors.push({
                  code: "validation_error",
                  message,
                  field,
                });
              });
            } else if (typeof messages === "string") {
              errors.push({
                code: "validation_error",
                message: messages,
                field,
              });
            }
          }

          return errors.length > 0
            ? errors
            : [
                {
                  code: "validation_error",
                  message: "Please check your input and try again.",
                },
              ];
        }

        // General bad request
        return {
          code: "bad_request",
          message:
            data.detail ||
            data.message ||
            "Invalid request. Please check your input.",
        };

      case 401: // Unauthorized
        return {
          code: "unauthorized",
          message:
            data.detail ||
            data.message ||
            "Authentication required. Please log in.",
        };

      case 403: // Forbidden
        return {
          code: "forbidden",
          message:
            data.detail ||
            data.message ||
            "You do not have permission to perform this action.",
        };

      case 404: // Not Found
        return {
          code: "not_found",
          message:
            data.detail ||
            data.message ||
            "The requested resource was not found.",
        };

      case 429: // Too Many Requests
        return {
          code: "rate_limited",
          message:
            data.detail ||
            data.message ||
            "Too many requests. Please try again later.",
        };

      case 500: // Server Error
      case 502: // Bad Gateway
      case 503: // Service Unavailable
      case 504: // Gateway Timeout
        return {
          code: "server_error",
          message:
            data.detail ||
            data.message ||
            "Server error. Please try again later.",
        };

      default:
        return {
          code: `http_${status}`,
          message:
            data.detail ||
            data.message ||
            "An error occurred. Please try again.",
        };
    }
  }

  // Handle network errors
  if (error.message && error.message.includes("Network Error")) {
    return {
      code: "network_error",
      message: "Network error. Please check your connection and try again.",
    };
  }

  // Handle timeout errors
  if (
    error.code === "ECONNABORTED" ||
    (error.message && error.message.includes("timeout"))
  ) {
    return {
      code: "timeout",
      message: "Request timed out. Please try again.",
    };
  }

  // Handle other error types
  if (error.error || error.message) {
    return {
      code: "api_error",
      message: error.error || error.message,
    };
  }

  // If we can't determine the error type, return default
  return defaultError;
}

export default {
  formatError,
  formatAuthError,
  formatValidationError,
  formatServerError,
  formatNetworkError,
  formatUnknownError,
  processApiError,
};
