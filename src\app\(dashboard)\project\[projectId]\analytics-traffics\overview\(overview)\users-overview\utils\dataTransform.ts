import type {
  CountriesAPIResponse,
  CountryMapData,
  CountryItem,
} from "../UsersOverview.type";
import { ProgressbarData } from "../../../../types/AnalyticsTraffics.types";

/**
 * Country name to ISO 3166-1 alpha-3 code mapping
 * Maps country names from API to the codes expected by ChoroplethMap
 */
const COUNTRY_NAME_TO_CODE: Record<string, string> = {
  Iran: "IRN",
  Germany: "DEU",
  "United States": "USA",
  France: "FRA",
  "United Kingdom": "GBR",
  Australia: "AUS",
  Finland: "FIN",
  Albania: "ALB",
  Canada: "CAN",
  India: "IND",
  China: "CHN",
  Japan: "JPN",
  Brazil: "BRA",
  Russia: "RUS",
  Italy: "ITA",
  Spain: "ESP",
  Netherlands: "NLD",
  Sweden: "SWE",
  Norway: "NOR",
  Denmark: "DNK",
  Belgium: "BEL",
  Switzerland: "CHE",
  Austria: "AUT",
  Poland: "POL",
  Turkey: "TUR",
  Türkiye: "TUR",
  "South Korea": "KOR",
  Mexico: "MEX",
  Argentina: "ARG",
  "South Africa": "ZAF",
  Egypt: "EGY",
  Israel: "ISR",
  "Saudi Arabia": "SAU",
  "United Arab Emirates": "ARE",
  Thailand: "THA",
  Singapore: "SGP",
  Malaysia: "MYS",
  Indonesia: "IDN",
  Philippines: "PHL",
  Vietnam: "VNM",
  "New Zealand": "NZL",
  Chile: "CHL",
  Colombia: "COL",
  Peru: "PER",
  Venezuela: "VEN",
  Ukraine: "UKR",
  "Czech Republic": "CZE",
  Hungary: "HUN",
  Romania: "ROU",
  Bulgaria: "BGR",
  Croatia: "HRV",
  Serbia: "SRB",
  Greece: "GRC",
  Portugal: "PRT",
  Ireland: "IRL",
  Iceland: "ISL",
  Luxembourg: "LUX",
  Malta: "MLT",
  Cyprus: "CYP",
  Estonia: "EST",
  Latvia: "LVA",
  Lithuania: "LTU",
  Slovenia: "SVN",
  Slovakia: "SVK",
  Belarus: "BLR",
  Moldova: "MDA",
  Georgia: "GEO",
  Armenia: "ARM",
  Azerbaijan: "AZE",
  Kazakhstan: "KAZ",
  Uzbekistan: "UZB",
  Kyrgyzstan: "KGZ",
  Tajikistan: "TJK",
  Turkmenistan: "TKM",
  Mongolia: "MNG",
  "North Korea": "PRK",
  Taiwan: "TWN",
  "Hong Kong": "HKG",
  Macau: "MAC",
  Bangladesh: "BGD",
  Pakistan: "PAK",
  Afghanistan: "AFG",
  "Sri Lanka": "LKA",
  Nepal: "NPL",
  Bhutan: "BTN",
  Maldives: "MDV",
  Myanmar: "MMR",
  Laos: "LAO",
  Cambodia: "KHM",
  Brunei: "BRN",
  "East Timor": "TLS",
  "Papua New Guinea": "PNG",
  Fiji: "FJI",
  "Solomon Islands": "SLB",
  Vanuatu: "VUT",
  Samoa: "WSM",
  Tonga: "TON",
  Kiribati: "KIR",
  Tuvalu: "TUV",
  Nauru: "NRU",
  Palau: "PLW",
  "Marshall Islands": "MHL",
  Micronesia: "FSM",
  Morocco: "MAR",
  Algeria: "DZA",
  Tunisia: "TUN",
  Libya: "LBY",
  Sudan: "SDN",
  "South Sudan": "SSD",
  Ethiopia: "ETH",
  Eritrea: "ERI",
  Djibouti: "DJI",
  Somalia: "SOM",
  Kenya: "KEN",
  Uganda: "UGA",
  Tanzania: "TZA",
  Rwanda: "RWA",
  Burundi: "BDI",
  "Democratic Republic of the Congo": "COD",
  "Republic of the Congo": "COG",
  "Central African Republic": "CAF",
  Chad: "TCD",
  Cameroon: "CMR",
  "Equatorial Guinea": "GNQ",
  Gabon: "GAB",
  "São Tomé and Príncipe": "STP",
  Nigeria: "NGA",
  Niger: "NER",
  Mali: "MLI",
  "Burkina Faso": "BFA",
  "Ivory Coast": "CIV",
  Ghana: "GHA",
  Togo: "TGO",
  Benin: "BEN",
  Liberia: "LBR",
  "Sierra Leone": "SLE",
  Guinea: "GIN",
  "Guinea-Bissau": "GNB",
  Gambia: "GMB",
  Senegal: "SEN",
  Mauritania: "MRT",
  "Cape Verde": "CPV",
  Angola: "AGO",
  Zambia: "ZMB",
  Zimbabwe: "ZWE",
  Botswana: "BWA",
  Namibia: "NAM",
  Lesotho: "LSO",
  Swaziland: "SWZ",
  Madagascar: "MDG",
  Mauritius: "MUS",
  Comoros: "COM",
  Seychelles: "SYC",
  Malawi: "MWI",
  Mozambique: "MOZ",
};

/**
 * Transform country name to ISO code
 * @param countryName - The country name from API
 * @returns The ISO 3166-1 alpha-3 code or the original name if not found
 */
export function getCountryCode(countryName: string): string {
  return COUNTRY_NAME_TO_CODE[countryName] || countryName;
}

/**
 * Transform GA4 API response data to ChoroplethMap format
 * @param apiResponse - The GA4 API response containing countries data
 * @param useAllDataForLeftMap - If true, use all countries for leftMap (for comparison mode)
 * @returns Transformed data for ChoroplethMap components
 */
export function transformCountriesData(
  apiResponse: CountriesAPIResponse,
  useAllDataForLeftMap: boolean = false
): CountryMapData {
  const { items } = apiResponse.data;

  // Sort countries by users count (descending) to get top countries
  const sortedCountries = [...items].sort((a, b) => b.users - a.users);

  // Transform to the format expected by ChoroplethMap
  // Format: "percentage,users,percentage" as string
  const leftMap: Record<string, string> = {};
  const rightMap: Record<string, string> = {};

  if (useAllDataForLeftMap) {
    // Use all countries for leftMap (comparison mode)
    sortedCountries.forEach((item) => {
      const countryCode = getCountryCode(item.name);
      leftMap[countryCode] = `${item.percentage.toFixed(1)},${
        item.users
      },${item.percentage.toFixed(1)}`;
    });
  } else {
    // Split into two groups for the two maps (single period mode)
    const midPoint = Math.ceil(sortedCountries.length / 2);
    const topHalf = sortedCountries.slice(0, midPoint);
    const bottomHalf = sortedCountries.slice(midPoint);

    topHalf.forEach((item) => {
      const countryCode = getCountryCode(item.name);
      leftMap[countryCode] = `${item.percentage.toFixed(1)},${
        item.users
      },${item.percentage.toFixed(1)}`;
    });

    bottomHalf.forEach((item) => {
      const countryCode = getCountryCode(item.name);
      rightMap[countryCode] = `${item.percentage.toFixed(1)},${
        item.users
      },${item.percentage.toFixed(1)}`;
    });
  }

  return {
    leftMap,
    rightMap,
  };
}

/**
 * Transform GA4 API response data to progress bar format
 * @param apiResponse - The GA4 API response containing countries data
 * @returns Array of progress bar data
 */
export function transformToProgressBarData(
  apiResponse: CountriesAPIResponse
): ProgressbarData[] {
  const { items } = apiResponse.data;

  // Sort by users count (descending) and take top 10
  const topCountries = [...items]
    .sort((a, b) => b.users - a.users)
    .slice(0, 10);

  return topCountries.map((item) => ({
    title: item.name,
    percentage: Math.round(item.percentage),
  }));
}

/**
 * Check if GA4 API response is valid
 * @param response - The GA4 API response to validate
 * @returns Boolean indicating if response is valid
 */
export function isValidCountriesResponse(
  response: any
): response is CountriesAPIResponse {
  return (
    response &&
    typeof response === "object" &&
    response.status === "success" &&
    response.data &&
    Array.isArray(response.data.items) &&
    response.data.totals &&
    typeof response.data.totals.total_users === "number"
  );
}

/**
 * Generate mock progress bar data for different tabs
 * NOTE: All tabs use the same countries data for maps, only progress bar data varies
 * @param tabName - The selected tab name
 * @returns Array of progress bar data specific to the tab
 */
export function generateMockProgressBarData(
  tabName: string
): ProgressbarData[] {
  // Define the age data that should be used as the base for all tabs
  const ageData: ProgressbarData[] = [
    { title: "18-24", percentage: 22 },
    { title: "25-34", percentage: 35 },
    { title: "35-44", percentage: 28 },
    { title: "45-54", percentage: 18 },
    { title: "55-64", percentage: 12 },
    { title: "65+", percentage: 8 },
  ];

  const mockData: Record<string, ProgressbarData[]> = {
    cities: [
      { title: "New York", percentage: 35 },
      { title: "London", percentage: 28 },
      { title: "Tokyo", percentage: 22 },
      { title: "Paris", percentage: 18 },
      { title: "Berlin", percentage: 15 },
      { title: "Sydney", percentage: 12 },
      { title: "Toronto", percentage: 8 },
    ],
    gender: [
      { title: "Male", percentage: 58 },
      { title: "Female", percentage: 42 },
    ],
    device: [
      { title: "Desktop", percentage: 45 },
      { title: "Mobile", percentage: 38 },
      { title: "Tablet", percentage: 17 },
    ],
    age: ageData,
  };

  const key = tabName.toLowerCase();
  // Always return the specific tab data, or age data as fallback
  return mockData[key] || ageData;
}
