export type TBar = {
  value: number;
  color: string;
  actualValue?: number; // Optional actual value for tooltip display
  period?: "current" | "previous"; // Optional period identifier for tooltip display
};

type isLoading = {
  label?: string;
  totalValue?: number;
  bars?: TBar[];
  className?: string;
  percentageLabel?: string;
  isLoading: true;
};

type isLoaded = {
  label: string;
  totalValue: number;
  bars: TBar[];
  className?: string;
  percentageLabel?: string;
  isLoading?: false;
};

export type THorizontalBar = isLoaded | isLoading;
