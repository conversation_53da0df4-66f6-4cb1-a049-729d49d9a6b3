import Title from "@/components/ui/Title";
import React, { useEffect } from "react";
import DateRange from "../date-range/DateRange";
import Badge from "../../analytic-insight/_components/Badge";
import { cn } from "@/utils/cn";
import { DataTableProps } from "./DataTable.types";
import { motion } from "framer-motion";

import TableSkeleton from "./TableSkeleton";
import { GoArrowSwitch } from "react-icons/go";
import {
  DndContext,
  closestCenter,
  PointerSensor,
  useSensor,
  useSensors,
  DragOverlay,
} from "@dnd-kit/core";
import {
  arrayMove,
  SortableContext,
  useSortable,
  horizontalListSortingStrategy,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import Checkbox from "@/components/ui/Checkbox";

/* ================================== MAIN ================================== */
const DataTable = ({
  tableData,
  badges,
  selectedItem,
  setSelectedItem,
  isLoading = false,
  title,
  showDateRange = true,
  checkbox = false,
  setSelectedItems,
  selectedItems,
}: DataTableProps) => {
  /* ========================================================================== */
  /*                                  CONSTANTS                                 */
  /* ========================================================================== */

  // Column order state
  const [columnOrder, setColumnOrder] = React.useState<number[]>(
    () => tableData?.tableHeadings.map((_, i) => i).slice(1) ?? []
  );
  useEffect(() => {
    if (tableData) {
      setColumnOrder(tableData.tableHeadings.map((_, i) => i).slice(1));
    }
  }, [tableData]);
  // dnd-kit sensors
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: { distance: 5 },
    })
  );
  const [activeId, setActiveId] = React.useState<number | null>(null);

  function SortableTh({
    id,
    children,
    ...props
  }: {
    id: number;
    children: React.ReactNode;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    [key: string]: any;
  }) {
    const {
      attributes,
      listeners,
      setNodeRef,
      transform,
      transition,
      isDragging,
    } = useSortable({ id });
    return (
      <th
        ref={setNodeRef}
        style={{
          transform: CSS.Transform.toString(transform),
          transition,
          zIndex: isDragging ? 2 : 1,
          background: isDragging ? "#e0e0e0" : undefined,
          width: 120, // fixed width for smoothness
          minWidth: 120,
          maxWidth: 180,
          cursor: "grab",
        }}
        {...attributes}
        {...listeners}
        {...props}
      >
        {children}
      </th>
    );
  }
  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */
  return (
    <div className="w-full space-y-10">
      {(title || showDateRange) && (
        <div className="space-y-2">
          {title && (
            <div className="flex w-full justify-between items-center">
              <Title>{title}</Title>
            </div>
          )}
          {showDateRange && <DateRange variation="short" />}
        </div>
      )}
      <div className="space-y-6">
        {badges && setSelectedItem && (
          <div className="flex gap-2 overflow-x-auto">
            {badges.map((badge, index) => (
              <Badge
                selected={selectedItem === badge}
                key={index}
                onSelect={() => setSelectedItem(badge)}
              >
                {badge}
              </Badge>
            ))}
          </div>
        )}
        <div className="overflow-x-auto">
          {isLoading ? (
            <motion.div
              key={"loading"}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.2 }}
            >
              <TableSkeleton />
            </motion.div>
          ) : (
            tableData && (
              <motion.div
                key={"data"}
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.2 }}
              >
                <DndContext
                  sensors={sensors}
                  collisionDetection={closestCenter}
                  onDragStart={(event) => {
                    const { active } = event;
                    setActiveId(active.id as number);
                  }}
                  onDragEnd={(event) => {
                    const { active, over } = event;
                    if (over && active.id !== over.id) {
                      const oldIndex = columnOrder.indexOf(active.id as number);
                      const newIndex = columnOrder.indexOf(over.id as number);
                      setColumnOrder(
                        arrayMove(columnOrder, oldIndex, newIndex)
                      );
                    }
                    setActiveId(null);
                  }}
                  onDragCancel={() => setActiveId(null)}
                >
                  <DragOverlay>
                    {activeId !== null && tableData ? (
                      <table className="bg-white border border-[#E0E0E0] rounded-md shadow-md text-xs text-secondary">
                        <thead>
                          <tr>
                            <th className="px-2 py-4 w-28 bg-[#F4F4F4] border-b-2 border-[#E0E0E0] text-center">
                              {tableData.tableHeadings[activeId] === "RF" ? (
                                <GoArrowSwitch
                                  className="rotate-90 mx-auto text-lg"
                                  strokeWidth={1}
                                />
                              ) : (
                                tableData.tableHeadings[activeId]
                              )}
                            </th>
                          </tr>
                        </thead>
                        <tbody>
                          {tableData.tableBody.map((row, rowIndex) => (
                            <tr key={rowIndex}>
                              <td className="px-2 py-4 text-sm text-center border-t border-[#E0E0E0] w-28 bg-white">
                                <span>{row[activeId].value}</span>
                                <br />
                                <span
                                  className={cn(
                                    "text-[10px]",
                                    row[activeId].growth?.includes("+") &&
                                      "text-primary-green",
                                    row[activeId].growth?.includes("-") &&
                                      "text-primary-red"
                                  )}
                                >
                                  {row[activeId].growth}
                                </span>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    ) : null}
                  </DragOverlay>

                  <table
                    className="w-full text-secondary text-xs"
                    style={{ tableLayout: "fixed" }}
                  >
                    <thead>
                      <tr>
                        <th
                          className={cn(
                            "px-2 text-center h-16 bg-[#F4F4F4] border-[#E0E0E0] border-b-2 rounded-tl-md w-40 max-w-48 cursor-default",
                            checkbox && "w-fit max-w-48"
                          )}
                        >
                          <div
                            className={cn(
                              checkbox &&
                                "flex items-center justify-start w-fit gap-2 pl-3"
                            )}
                          >
                            {checkbox && (
                              <Checkbox
                                checked={
                                  selectedItems?.includes(
                                    tableData.tableHeadings[0]
                                  ) || false
                                }
                                onChange={() => {
                                  if (!setSelectedItems) return;
                                  const allItems = tableData.tableBody
                                    .map((row) => row[0].value)
                                    .concat(tableData.tableHeadings[0]);
                                  const allSelected =
                                    selectedItems?.length ===
                                    tableData.tableBody.length + 1;

                                  setSelectedItems(allSelected ? [] : allItems);
                                }}
                                id={"checkbox-0"}
                              />
                            )}
                            {tableData.tableHeadings[0]}
                          </div>
                        </th>
                        <SortableContext
                          items={columnOrder}
                          strategy={horizontalListSortingStrategy}
                        >
                          {columnOrder.map((colIdx, index) => {
                            const isDraggingThisColumn = activeId === colIdx;
                            return (
                              <SortableTh
                                id={colIdx}
                                key={colIdx}
                                style={{
                                  visibility: isDraggingThisColumn
                                    ? "hidden"
                                    : "visible",
                                }}
                                className={`px-2 max-w-20 first:max-w-32 first:w-28 text-center h-16 bg-[#F4F4F4] border-[#E0E0E0] cursor-grab ${
                                  index === columnOrder.length - 1 &&
                                  "rounded-tr-md"
                                } ${!isDraggingThisColumn && " border-b-2"}`}
                              >
                                {tableData.tableHeadings[colIdx] === "RF" ? (
                                  <GoArrowSwitch className="rotate-90 mx-auto text-lg" />
                                ) : (
                                  tableData.tableHeadings[colIdx]
                                )}
                              </SortableTh>
                            );
                          })}
                        </SortableContext>
                      </tr>
                    </thead>
                    <tbody>
                      {tableData &&
                        tableData.tableBody.map((row, rowIndex) => {
                          const isLastRow =
                            rowIndex === tableData.tableBody.length - 1;
                          return (
                            <tr key={rowIndex}>
                              <td
                                className={`px-2 py-4 text-sm text-center border-0 w-40 max-w-48 rounded-none ${
                                  rowIndex % 2 !== 0
                                    ? "bg-[#F4F4F4] border-y-2"
                                    : ""
                                } ${
                                  isLastRow ? "border-b-0 rounded-bl-2xl" : ""
                                }`}
                              >
                                <div
                                  className={cn(
                                    "flex items-center gap-2 justify-start w-full pl-2 whitespace-nowrap"
                                  )}
                                >
                                  {checkbox && (
                                    <Checkbox
                                      checked={
                                        selectedItems?.includes(row[0].value) ||
                                        false
                                      }
                                      onChange={() => {
                                        if (setSelectedItems) {
                                          setSelectedItems((prev) => {
                                            if (prev.includes(row[0].value)) {
                                              return prev.filter(
                                                (item) => item !== row[0].value
                                              );
                                            }
                                            return [...prev, row[0].value];
                                          });
                                        }
                                      }}
                                      id={"checkbox-0"}
                                      className="shrink-0"
                                    />
                                  )}
                                  <span className="whitespace-nowrap text-left">
                                    {row[0].value}
                                  </span>
                                </div>
                                <span
                                  className={cn(
                                    "text-[10px]",
                                    row[0].growth?.includes("+") &&
                                      "text-primary-green",
                                    row[0].growth?.includes("-") &&
                                      "text-primary-red"
                                  )}
                                >
                                  {row[0].growth}
                                </span>
                              </td>
                              {columnOrder.map((colIdx, cellIndex) => {
                                const cell = row[colIdx];
                                const isFirst = cellIndex === 0;
                                const isLast = cellIndex === row.length - 1;
                                const isDraggingThisColumn =
                                  activeId === colIdx;
                                return (
                                  <td
                                    key={colIdx}
                                    className={`px-2 py-4 text-sm rounded-none border-0 text-center
                                        ${
                                          rowIndex % 2 !== 0 &&
                                          !isDraggingThisColumn
                                            ? "bg-[#F4F4F4] border-y-2"
                                            : ""
                                        }
                                        ${
                                          isLastRow && isFirst
                                            ? "rounded-none"
                                            : isLastRow && isLast
                                            ? "rounded-br-2xl"
                                            : ""
                                        } ${isLastRow && "border-b-0"}`}
                                    style={{
                                      width: 120,
                                      minWidth: 120,
                                      maxWidth: 180,
                                      visibility: isDraggingThisColumn
                                        ? "hidden"
                                        : "visible",
                                    }}
                                  >
                                    <span className="text-sm">
                                      {cell.value.includes("/") ? (
                                        <div>
                                          <span className="text-primary-red">
                                            {cell.value.split("/")[0]}
                                          </span>
                                          <span>/</span>
                                          <span className="text-primary-green">
                                            {cell.value.split("/")[1]}
                                          </span>
                                        </div>
                                      ) : (
                                        cell.value
                                      )}
                                    </span>
                                    <span
                                      className={cn(
                                        "text-[10px]",
                                        cell.growth?.includes("+") &&
                                          "text-primary-green",
                                        cell.growth?.includes("-") &&
                                          "text-primary-red"
                                      )}
                                    >
                                      {cell.growth}
                                    </span>
                                  </td>
                                );
                              })}
                            </tr>
                          );
                        })}
                    </tbody>
                  </table>
                </DndContext>
              </motion.div>
            )
          )}
        </div>
      </div>
    </div>
  );
};

export default DataTable;
