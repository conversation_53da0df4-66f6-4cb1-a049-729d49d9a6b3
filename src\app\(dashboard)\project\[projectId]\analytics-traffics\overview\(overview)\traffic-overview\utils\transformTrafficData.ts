import type {
  TrafficOverviewData,
  BarsData,
  Metric,
  ComparisonTrafficOverviewData,
} from "../../../../types/HorizontalBars.types";

// Traffic source labels for display
const TRAFFIC_SOURCE_LABELS = {
  organic: "Organic",
  paid: "Paid",
  direct: "Direct",
  social: "Social",
  referral: "Referral",
  email: "Email",
  unassigned: "Unassigned",
} as const;

/**
 * Transform comparison traffic overview data for a specific metric into chart format
 *
 * Visual Logic:
 * - Growth (current > previous): Yellow base (previous) + Purple top (difference)
 * - Decline (current < previous): Purple base (current) + Yellow top (difference)
 * - Shows both values in tooltip with period labels
 *
 * Colors:
 * - Current period: Purple (#914AC4)
 * - Previous period: Yellow (#FFCD29)
 */
export const transformComparisonMetricToChartData = (
  currentMetric: Metric,
  previousMetric: Metric,
  metricName: string
): BarsData => {
  // Get all traffic sources that have data in either period
  const allSources = new Set([
    ...Object.keys(currentMetric.traffic_sources),
    ...Object.keys(previousMetric.traffic_sources),
  ]);

  const trafficSourceBars = Array.from(allSources)
    .filter((key) => {
      const currentValue =
        currentMetric.traffic_sources[
          key as keyof typeof currentMetric.traffic_sources
        ]?.value || 0;
      const previousValue =
        previousMetric.traffic_sources[
          key as keyof typeof previousMetric.traffic_sources
        ]?.value || 0;
      return currentValue > 0 || previousValue > 0;
    })
    .map((key) => {
      const currentValue =
        currentMetric.traffic_sources[
          key as keyof typeof currentMetric.traffic_sources
        ]?.value || 0;
      const previousValue =
        previousMetric.traffic_sources[
          key as keyof typeof previousMetric.traffic_sources
        ]?.value || 0;

      const sourceName =
        TRAFFIC_SOURCE_LABELS[key as keyof typeof TRAFFIC_SOURCE_LABELS];

      const barData = [];

      // Always include both period data for tooltip, but only show visual bars for non-zero values
      const periodData = {
        current: { value: currentValue, color: "bg-[#914AC4]" },
        previous: { value: previousValue, color: "bg-[#FFCD29]" },
      };

      // Case 1: Growth (current > previous) - e.g., current: 13.5k, previous: 12.4k
      if (currentValue > previousValue && previousValue > 0) {
        // Show previous period value in yellow (base layer) - shows 12.4k
        barData.push({
          value: previousValue,
          color: "bg-[#FFCD29]",
          actualValue: previousValue,
          period: "previous",
        });
        // Show the growth difference in primary color (additional layer) - shows remaining 1.1k visually
        barData.push({
          value: currentValue - previousValue,
          color: "bg-[#914AC4]",
          actualValue: currentValue, // Full current value for tooltip
          period: "current",
        });
      }
      // Case 2: Decline (current < previous) - e.g., current: 1.4k, previous: 2.8k
      else if (currentValue < previousValue && currentValue > 0) {
        // Show current period value in primary color (base layer) - shows 1.4k
        barData.push({
          value: currentValue,
          color: "bg-[#914AC4]",
          actualValue: currentValue,
          period: "current",
        });
        // Show the decline difference in yellow (additional layer) - shows remaining 1.4k visually
        barData.push({
          value: previousValue - currentValue,
          color: "bg-[#FFCD29]",
          actualValue: previousValue, // Full previous value for tooltip
          period: "previous",
        });
      }
      // Case 3: Only current value exists (no previous)
      else if (currentValue > 0 && previousValue === 0) {
        barData.push({
          value: currentValue,
          color: "bg-[#914AC4]",
          actualValue: currentValue,
          period: "current",
        });
        // Add invisible previous period data for tooltip consistency
        barData.push({
          value: 0,
          color: "bg-[#FFCD29]",
          actualValue: previousValue,
          period: "previous",
        });
      }
      // Case 4: Only previous value exists (no current)
      else if (previousValue > 0 && currentValue === 0) {
        barData.push({
          value: previousValue,
          color: "bg-[#FFCD29]",
          actualValue: previousValue,
          period: "previous",
        });
        // Add invisible current period data for tooltip consistency
        barData.push({
          value: 0,
          color: "bg-[#914AC4]",
          actualValue: currentValue,
          period: "current",
        });
      }
      // Case 5: Same values
      else if (currentValue === previousValue && currentValue > 0) {
        // When values are equal, show current period visually
        barData.push({
          value: currentValue,
          color: "bg-[#914AC4]",
          actualValue: currentValue,
          period: "current",
        });
        // Add previous period data for tooltip
        barData.push({
          value: 0,
          color: "bg-[#FFCD29]",
          actualValue: previousValue,
          period: "previous",
        });
      }
      // Case 6: Both values are zero
      else if (currentValue === 0 && previousValue === 0) {
        barData.push({
          value: 0,
          color: "bg-[#914AC4]",
          actualValue: currentValue,
          period: "current",
        });
        barData.push({
          value: 0,
          color: "bg-[#FFCD29]",
          actualValue: previousValue,
          period: "previous",
        });
      }

      return {
        label: sourceName,
        barData,
      };
    });

  // If no traffic sources have data, show a placeholder
  const finalBars =
    trafficSourceBars.length > 0
      ? trafficSourceBars
      : [
          {
            label: "No Data",
            barData: [{ value: 0, color: "bg-gray-300" }],
          },
        ];

  // Use the maximum total value from both periods for proper scaling
  const maxValue = Math.max(
    currentMetric.total_value,
    previousMetric.total_value,
    1
  );

  return {
    maxValue,
    bars: finalBars,
  };
};

/**
 * Get metric data by metric name
 */
export const getMetricByName = (
  data: TrafficOverviewData,
  metricName: string
): Metric | null => {
  const metricMap: Record<string, keyof TrafficOverviewData["metrics"]> = {
    "Total Users": "total_users",
    "New Users": "new_users",
    Sessions: "sessions",
    "Active Users": "active_users",
    Views: "page_views",
    "Event Count": "event_count",
    Conversions: "conversions",
  };

  const metricKey = metricMap[metricName];
  return metricKey ? data.metrics[metricKey] : null;
};

/**
 * Format number for display (e.g., 1000 -> 1K)
 */
export const formatMetricValue = (value: number): string => {
  if (value >= 1000000) {
    return `${(value / 1000000).toFixed(1)}M`;
  }
  if (value >= 1000) {
    return `${(value / 1000).toFixed(1)}K`;
  }
  return value.toString();
};

/**
 * Helper function to create empty traffic sources structure
 */
export const getEmptyTrafficSources = () => ({
  organic: { value: 0, percentage: 0 },
  paid: { value: 0, percentage: 0 },
  direct: { value: 0, percentage: 0 },
  social: { value: 0, percentage: 0 },
  referral: { value: 0, percentage: 0 },
  email: { value: 0, percentage: 0 },
  unassigned: { value: 0, percentage: 0 },
});

/**
 * Calculate percentage change between two values
 */
export const calculateChange = (current: number, previous: number): string => {
  if (previous === 0) {
    return current > 0 ? "+100%" : "0%";
  }
  const change = ((current - previous) / previous) * 100;
  const sign = change >= 0 ? "+" : "";
  return `${sign}${change.toFixed(1)}%`;
};

/**
 * Generate traffic overview tabs with comparison data
 */
export const generateTrafficOverviewTabs = (
  comparisonData: ComparisonTrafficOverviewData | null
) => {
  if (!comparisonData?.current || !comparisonData?.previous) {
    return [
      { id: 1, title: "Total Users", value: "0", changeValue: "+0.0%" },
      { id: 2, title: "New Users", value: "0", changeValue: "+0.0%" },
      { id: 3, title: "Sessions", value: "0", changeValue: "+0.0%" },
      { id: 4, title: "Active Users", value: "0", changeValue: "+0.0%" },
      { id: 5, title: "Views", value: "0", changeValue: "+0.0%" },
      { id: 6, title: "Event Count", value: "0", changeValue: "+0.0%" },
      { id: 7, title: "Conversions", value: "0", changeValue: "+0.0%" },
    ];
  }

  const currentMetrics = comparisonData.current.metrics;
  const previousMetrics = comparisonData.previous.metrics;

  return [
    {
      id: 1,
      title: "Total Users",
      value: formatMetricValue(currentMetrics.total_users.total_value),
      changeValue: calculateChange(
        currentMetrics.total_users.total_value,
        previousMetrics.total_users.total_value
      ),
    },
    {
      id: 2,
      title: "New Users",
      value: formatMetricValue(currentMetrics.new_users.total_value),
      changeValue: calculateChange(
        currentMetrics.new_users.total_value,
        previousMetrics.new_users.total_value
      ),
    },
    {
      id: 3,
      title: "Sessions",
      value: formatMetricValue(currentMetrics.sessions.total_value),
      changeValue: calculateChange(
        currentMetrics.sessions.total_value,
        previousMetrics.sessions.total_value
      ),
    },
    {
      id: 4,
      title: "Active Users",
      value: formatMetricValue(currentMetrics.active_users.total_value),
      changeValue: calculateChange(
        currentMetrics.active_users.total_value,
        previousMetrics.active_users.total_value
      ),
    },
    {
      id: 5,
      title: "Views",
      value: formatMetricValue(currentMetrics.page_views.total_value),
      changeValue: calculateChange(
        currentMetrics.page_views.total_value,
        previousMetrics.page_views.total_value
      ),
    },
    {
      id: 6,
      title: "Event Count",
      value: formatMetricValue(currentMetrics.event_count.total_value),
      changeValue: calculateChange(
        currentMetrics.event_count.total_value,
        previousMetrics.event_count.total_value
      ),
    },
    {
      id: 7,
      title: "Conversions",
      value: formatMetricValue(currentMetrics.conversions.total_value),
      changeValue: calculateChange(
        currentMetrics.conversions.total_value,
        previousMetrics.conversions.total_value
      ),
    },
  ];
};
