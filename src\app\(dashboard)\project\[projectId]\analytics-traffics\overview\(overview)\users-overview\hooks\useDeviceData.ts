import httpService from "@/services/httpService";
import { useQuery } from "@tanstack/react-query";
import { useProjectId } from "@/hooks/useProjectId";
import { useDateRangeStore } from "@/store/useDateRangeStore";
import { ProgressbarData } from "../../../../types/AnalyticsTraffics.types";
import type { DeviceAPIResponse } from "../UsersOverview.type";

/**
 * Check if Device API response is valid
 * @param response - The Device API response to validate
 * @returns Boolean indicating if response is valid
 */
function isValidDeviceResponse(response: any): response is DeviceAPIResponse {
  return (
    response &&
    typeof response === "object" &&
    response.status === "success" &&
    response.data &&
    Array.isArray(response.data.items) &&
    response.data.totals &&
    typeof response.data.totals.total_users === "number"
  );
}

/**
 * Transform Device API response data to progress bar format
 * @param apiResponse - The Device API response containing device data
 * @returns Array of progress bar data
 */
function transformDeviceToProgressBarData(
  apiResponse: DeviceAPIResponse
): ProgressbarData[] {
  const { items } = apiResponse.data;

  // Sort by users count (descending) and take all items (usually Desktop, Mobile, Tablet)
  const sortedDevices = [...items].sort((a, b) => b.users - a.users);

  return sortedDevices.map((item) => ({
    title: item.name,
    percentage: Math.round(item.percentage),
  }));
}

/**
 * Custom hook for fetching device data from GA4 API (for progress bars)
 * This hook is only called when the Device tab is selected
 * @param enabled - Whether to enable the query (should be true only for Device tab)
 * @returns Query result with device progress bar data
 */
const useDeviceData = (enabled: boolean = false) => {
  const { projectId, isValidProjectId } = useProjectId();
  const { getFormattedDates } = useDateRangeStore();

  // Get formatted dates for API call (YYYY-MM-DD format)
  const { startDate, endDate } = getFormattedDates();

  return useQuery({
    queryKey: [
      "user-device-data",
      projectId,
      startDate,
      endDate,
    ],
    queryFn: async (): Promise<ProgressbarData[]> => {
      if (!projectId) {
        throw new Error("Project ID is required");
      }

      // Build API URL with GA4 endpoint format for user device
      const apiUrl = `/api/project/GA4/user/overview/device/${projectId}/`;

      try {
        // Fetch current period data
        const currentParams: Record<string, string> = {};
        if (startDate && endDate) {
          currentParams.start_date = startDate;
          currentParams.end_date = endDate;
        }

        const response = await httpService.get(apiUrl, {
          params:
            Object.keys(currentParams).length > 0 ? currentParams : undefined,
          useAuth: true,
        });

        // Validate response
        if (!isValidDeviceResponse(response.data)) {
          throw new Error("Invalid device API response format");
        }

        // Transform to progress bar data
        return transformDeviceToProgressBarData(response.data);
      } catch (error) {
        console.error("Device API call failed:", error);
        
        // Re-throw the error to let React Query handle it
        throw new Error(
          error instanceof Error
            ? `Failed to fetch user device data: ${error.message}`
            : "Failed to fetch user device data"
        );
      }
    },
    enabled: enabled && isValidProjectId && !!projectId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  });
};

export default useDeviceData;
