"use client";
import React from "react";
import { motion } from "framer-motion";

/* =============================== COMPONENTS =============================== */
import Card from "@/components/ui/card";
import Title from "@/components/ui/Title";
import DateRange from "../../../../_components/date-range/DateRange";
import GSCLineChart from "../../../_components/line-chart/LineChart";
import LineChartCard from "../../../../overview/_components/LineChartCard";
import LineChartSkeleton from "../../../../_components/line-chart-skeleton/LineChartSkeleton";
import SmallChartSkeleton from "../../../../_components/small-chart-skeleton/SmallChartSkeleton";
import NoData from "../../../_components/NoData";
import ErrorDisplay from "../../../_components/ErrorDisplay";

/* ================================== HOOKS ================================= */
import useAudience from "../Audience.hook";

/* ================================== TYPES ================================= */
import type { LineChartCards } from "../Audience.types";

/* ================================= ZUSTAND ================================ */
import {
  useLineChartDataStore,
  useChartPopupStore,
} from "@/store/useChartPopupStore";

/* ========================================================================== */
const NewUsersAudience: React.FC = () => {
  /* ========================================================================== */
  /*                                  CONSTANTS                                 */
  /* ========================================================================== */
  const activeTab = "New Users";
  const { data, isLoading, isPending, isError, error, refetch } =
    useAudience(activeTab);

  // Chart popup functionality
  const setChartData = useLineChartDataStore((setData) => setData.setChartData);
  const { show } = useChartPopupStore();

  /* ========================================================================== */
  /*                                  HANDLERS                                  */
  /* ========================================================================== */
  const handleSetChartData = (chart: LineChartCards) => {
    // Transform the chart data to match the expected format
    const transformedData = chart.data.map((item) => ({
      name: item.name,
      value: item.value,
      comparisonValue: (item as any).comparisonValue,
    }));

    // Check if we have comparison data
    const hasComparison = transformedData.some(
      (item) => item.comparisonValue !== undefined
    );

    setChartData({
      title: chart.title,
      bigNumber: chart.bigNumber,
      smallNumber: chart.smallNumber,
      data: transformedData,
      hasComparison: hasComparison,
    });
    show();
  };

  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */

  // Handle error state
  if (isError) {
    const errorCode = (error as any)?.code;
    const errorStatus =
      (error as any)?.response?.status || (error as any)?.status;

    if (errorCode === "not_found" || errorStatus === 404) {
      return <NoData title="New Users Audience" />;
    }

    return (
      <ErrorDisplay
        error={error}
        onRetry={() => refetch()}
        title="New Users Audience Data"
        className="min-h-[400px]"
      />
    );
  }

  // Handle no data state
  if (!data && !isLoading && !isPending) {
    return <NoData title="New Users Audience" />;
  }

  return (
    <Card className="space-y-2">
      <div>
        <Title>New Users Audience</Title>
      </div>
      <DateRange />

      {/* Main Chart */}
      <div
        className="mt-8 min-h-[310px] overflow-hidden"
        style={{ position: "relative" }}
      >
        {isLoading || isPending ? (
          <motion.div
            key="loading"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
          >
            <LineChartSkeleton />
          </motion.div>
        ) : data ? (
          <motion.div
            key="data"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
          >
            <GSCLineChart
              lineChartData={data.lineChartData}
              colors={data.colors}
              selectedLines={data.selectedLines}
              cardsData={data.cardsData}
            />
          </motion.div>
        ) : null}
      </div>

      {/* Small Charts */}
      <div className="flex flex-col lg:flex-row w-full mt-8 lg:gap-2 px-8 min-h-[170px]">
        {isLoading || isPending
          ? Array.from({ length: 3 }).map((_, i) => (
              <motion.div
                className="w-full"
                key={`loading-${i}`}
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.3 }}
              >
                <SmallChartSkeleton className="w-full" />
              </motion.div>
            ))
          : data &&
            data.lineChartCards
              .filter((item) => item.title === "New Users")
              .map((item: LineChartCards, index: number) => (
                <motion.div
                  key={`card-${index}`}
                  className="w-full"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  transition={{ duration: 0.3 }}
                >
                  <LineChartCard
                    data={item.data}
                    bigNumber={item.bigNumber}
                    smallNumber={item.smallNumber}
                    title={item.title}
                    className="w-full cursor-pointer"
                    onClick={() => handleSetChartData(item)}
                  />
                </motion.div>
              ))}
      </div>
    </Card>
  );
};

export default NewUsersAudience;
