import type {
  GA4ApiResponse,
  GA4DailyMetric,
  GA4Totals,
  AudienceTabConfig,
  AudienceResponse,
  LineChartCards,
} from "./Audience.types";
import type {
  CardTabType,
  LineChartPoint,
  ColorConfig,
  CardsData,
} from "../../../types/AnalyticsTraffics.types";

/**
 * Formats a number to a readable string with appropriate suffixes
 */
export const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + "M";
  }
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + "K";
  }
  return num.toString();
};

/**
 * Formats percentage to display with % symbol
 * Note: API returns percentage values as raw numbers (e.g., 15.42 for 15.42%)
 */
export const formatPercentage = (rate: number): string => {
  return rate.toFixed(1) + "%";
};

/**
 * Formats time in seconds to readable format
 */
export const formatTime = (seconds: number): string => {
  if (seconds >= 60) {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}m ${remainingSeconds}s`;
  }
  return `${Math.floor(seconds)}s`;
};

/**
 * Formats date from YYYYMMDD or YYYY-MM-DD to readable format
 */
export const formatDate = (dateStr: string): string => {
  let formattedDateStr = dateStr;

  // If the date is in YYYYMMDD format, convert it to YYYY-MM-DD
  if (dateStr.length === 8 && !dateStr.includes("-")) {
    const year = dateStr.substring(0, 4);
    const month = dateStr.substring(4, 6);
    const day = dateStr.substring(6, 8);
    formattedDateStr = `${year}-${month}-${day}`;
  }

  const date = new Date(formattedDateStr);
  return date.toLocaleDateString("en-US", {
    month: "short",
    day: "numeric",
  });
};

/**
 * Calculates growth percentage between current and previous period
 */
export const calculateGrowth = (current: number, previous: number): string => {
  if (previous === 0) return "+0%";
  const growth = ((current - previous) / previous) * 100;
  const sign = growth >= 0 ? "+" : "";
  return `${sign}${growth.toFixed(1)}%`;
};

/**
 * Formats value based on type
 */
export const formatValue = (
  value: number,
  type: "number" | "percentage" | "time"
): string => {
  switch (type) {
    case "number":
      return formatNumber(value);
    case "percentage":
      return formatPercentage(value);
    case "time":
      return formatTime(value);
    default:
      return value.toString();
  }
};

/**
 * Configuration for audience tabs
 */
export const AUDIENCE_TABS: AudienceTabConfig[] = [
  {
    key: "total-users",
    title: "Total Users",
    endpoint: "/api/project/GA4/traffic/overview/total-users",
    primaryMetric: "total_users",
    chartMetric: "total_users",
    formatType: "number",
  },
  {
    key: "new-users",
    title: "New Users",
    endpoint: "/api/project/GA4/traffic/overview/new-users",
    primaryMetric: "new_users",
    chartMetric: "new_users",
    formatType: "number",
  },
  {
    key: "returning-users",
    title: "Returning Users",
    endpoint: "/api/project/GA4/traffic/overview/returning-users",
    primaryMetric: "returning_users",
    chartMetric: "returning_users",
    formatType: "number",
  },
  {
    key: "active-users",
    title: "Active Users",
    endpoint: "/api/project/GA4/traffic/overview/active-users",
    primaryMetric: "active_users",
    chartMetric: "active_users",
    formatType: "number",
  },
  {
    key: "sessions",
    title: "Sessions",
    endpoint: "/api/project/GA4/traffic/overview/sessions",
    primaryMetric: "sessions",
    chartMetric: "sessions",
    formatType: "number",
  },
  {
    key: "engaged-sessions",
    title: "Engaged Sessions",
    endpoint: "/api/project/GA4/traffic/overview/engaged-sessions",
    primaryMetric: "engaged_sessions",
    chartMetric: "engaged_sessions",
    formatType: "number",
  },
  {
    key: "views",
    title: "Views",
    endpoint: "/api/project/GA4/traffic/overview/views",
    primaryMetric: "views",
    chartMetric: "views",
    formatType: "number",
  },
  {
    key: "avg-engagement-time",
    title: "Avg. Engagement Time",
    endpoint: "/api/project/GA4/traffic/overview/avg-engagement-time",
    primaryMetric: "avg_engagement_time",
    chartMetric: "avg_engagement_time",
    formatType: "time",
  },
];

/**
 * Configuration for small chart cards (only these three metrics)
 */
export const SMALL_CHART_TABS: AudienceTabConfig[] = [
  {
    key: "engagement-rate",
    title: "Engagement Rate",
    endpoint: "/api/project/GA4/traffic/overview/engagement-rate",
    primaryMetric: "engagement_rate",
    chartMetric: "engagement_rate",
    formatType: "percentage",
  },
  {
    key: "active-users-rate",
    title: "Active User Rate",
    endpoint: "/api/project/GA4/traffic/overview/active-users",
    primaryMetric: "active_users_rate",
    chartMetric: "active_users_rate",
    formatType: "percentage",
  },
  {
    key: "returning-users-rate",
    title: "Returning User Rate",
    endpoint: "/api/project/GA4/traffic/overview/returning-users-rate",
    primaryMetric: "returning_users_rate",
    chartMetric: "returning_users_rate",
    formatType: "percentage",
  },
];

/**
 * Default colors for the charts
 */
export const DEFAULT_COLORS: ColorConfig[] = [
  { name: "Total Users", color: "#914AC4" }, // Primary purple color
  { name: "New Users", color: "#FFCD29" }, // Yellow secondary color
  { name: "Returning Users", color: "#914AC4" }, // Primary purple color
];

/**
 * Transforms GA4 API responses into the format expected by the Audience component
 */
export const transformAudienceData = (
  activeTab: string,
  primaryResponses: Record<string, GA4ApiResponse>,
  comparisonResponses?: Record<string, GA4ApiResponse> | null
): AudienceResponse => {
  const hasComparison = !!comparisonResponses;

  // Create card tabs data
  const cardTabs: CardTabType[] = AUDIENCE_TABS.map((tab) => {
    const primaryData = primaryResponses[tab.key];
    const comparisonData = comparisonResponses?.[tab.key];

    if (!primaryData) {
      return {
        title: tab.title,
        value: "0",
        changeValue: "+0%",
      };
    }

    const primaryValue = primaryData.data.totals[tab.primaryMetric] || 0;
    const comparisonValue = comparisonData?.data.totals[tab.primaryMetric] || 0;

    return {
      title: tab.title,
      value: formatValue(primaryValue, tab.formatType),
      changeValue: hasComparison
        ? calculateGrowth(primaryValue, comparisonValue)
        : "+0%",
    };
  });

  // Find the active tab configuration
  const activeTabConfig = AUDIENCE_TABS.find((tab) => tab.title === activeTab);
  const activeTabKey = activeTabConfig?.key || AUDIENCE_TABS[0].key;
  const activeResponse = primaryResponses[activeTabKey];
  const activeComparisonResponse = comparisonResponses?.[activeTabKey];

  if (!activeResponse) {
    // Return empty data structure if no active response
    return {
      cardTabs,
      lineChartData: [],
      colors: DEFAULT_COLORS,
      selectedLines: [],
      cardsData: {},
      lineChartCards: [],
    };
  }

  // Create line chart data for the main chart
  const lineChartData: LineChartPoint[] = activeResponse.data.daily_metrics.map(
    (metric, index) => {
      const comparisonMetric =
        activeComparisonResponse?.data.daily_metrics[index];
      const chartMetric = activeTabConfig?.chartMetric || "total_users";

      return {
        name: formatDate(metric.date),
        clicks: metric[chartMetric] || 0,
        impressions: comparisonMetric?.[chartMetric] || 0,
      };
    }
  );

  // Create colors configuration
  const colors: ColorConfig[] = [
    { name: "clicks", color: "#914AC4" }, // Primary purple color
    ...(hasComparison ? [{ name: "impressions", color: "#FFCD29" }] : []), // Yellow secondary color
  ];

  // Selected lines for the chart
  const selectedLines = hasComparison ? ["clicks", "impressions"] : ["clicks"];

  // Cards data for the main chart tooltip
  const cardsData: CardsData = {
    clicks: {
      amount:
        activeResponse.data.totals[
          activeTabConfig?.primaryMetric || "total_users"
        ] || 0,
      growth:
        hasComparison && activeComparisonResponse
          ? calculateGrowth(
              activeResponse.data.totals[
                activeTabConfig?.primaryMetric || "total_users"
              ] || 0,
              activeComparisonResponse.data.totals[
                activeTabConfig?.primaryMetric || "total_users"
              ] || 0
            )
          : "+0%",
    },
    ...(hasComparison && activeComparisonResponse
      ? {
          impressions: {
            amount:
              activeComparisonResponse.data.totals[
                activeTabConfig?.primaryMetric || "total_users"
              ] || 0,
            growth: "+0%", // Comparison period doesn't have its own growth
          },
        }
      : {}),
  };

  // Create small chart cards data
  const lineChartCards: LineChartCards[] = SMALL_CHART_TABS.map((tab) => {
    const tabResponse = primaryResponses[tab.key];
    const tabComparisonResponse = comparisonResponses?.[tab.key];

    if (!tabResponse) {
      return {
        title: tab.title,
        bigNumber: "0",
        smallNumber: "+0%",
        data: [],
      };
    }

    const primaryValue = tabResponse.data.totals[tab.primaryMetric] || 0;
    const comparisonValue =
      tabComparisonResponse?.data.totals[tab.primaryMetric] || 0;

    // Create chart data for the small cards
    const chartData = tabResponse.data.daily_metrics.map((metric, index) => {
      const comparisonMetric = tabComparisonResponse?.data.daily_metrics[index];
      return {
        name: formatDate(metric.date),
        value: metric[tab.chartMetric] || 0,
        comparisonValue: comparisonMetric?.[tab.chartMetric],
      };
    });

    return {
      title: tab.title,
      bigNumber: formatValue(primaryValue, tab.formatType),
      smallNumber: hasComparison
        ? calculateGrowth(primaryValue, comparisonValue)
        : "+0%",
      data: chartData,
    };
  });

  return {
    cardTabs,
    lineChartData,
    colors,
    selectedLines,
    cardsData,
    lineChartCards,
  };
};
