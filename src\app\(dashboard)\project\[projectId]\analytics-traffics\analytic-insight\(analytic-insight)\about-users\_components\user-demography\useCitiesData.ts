import React from "react";
import { useQuery } from "@tanstack/react-query";
import httpService from "@/services/httpService";
import { useProjectId } from "@/hooks/useProjectId";
import { useDateRangeStore } from "@/store/useDateRangeStore";
import { TableDataRequest } from "../../../../../_components/data-table/DataTable.types";

// Types for the API response
interface CityData {
  name: string;
  total_users: number;
  active_users: number;
  new_users: number;
  returning_users: number;
  sessions: number;
  engaged_sessions: number;
  views: number;
  avg_engagement_time: number;
  engagement_rate: number;
  event_count: number;
  conversions: number;
  percentage: number;
}

interface CitiesApiResponse {
  status: string;
  project_id: string;
  metric: string;
  data: {
    dimension_breakdown: CityData[];
    daily_metrics: any[];
  };
}

// Helper function to calculate percentage change
const calculatePercentageChange = (
  current: number,
  previous: number
): string => {
  if (previous === 0) {
    return current > 0 ? "+100%" : "0%";
  }
  const change = ((current - previous) / previous) * 100;
  const sign = change >= 0 ? "+" : "";
  return `${sign}${change.toFixed(1)}%`;
};

// Helper function to format numbers
const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + "M";
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + "K";
  }
  return num.toString();
};

// Helper function to format engagement time
const formatEngagementTime = (seconds: number): string => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = Math.floor(seconds % 60);
  return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
};

// Helper function to format percentage
const formatPercentage = (rate: number): string => {
  return `${(rate * 100).toFixed(1)}%`;
};

// First, create a hook that fetches all the data
const useCitiesDataRaw = () => {
  const { projectId, isValidProjectId } = useProjectId();
  const { getFormattedDates, isComparisonEnabled } = useDateRangeStore();

  return useQuery({
    queryKey: ["cities-data", projectId, getFormattedDates()],
    queryFn: async () => {
      if (!isValidProjectId || !projectId) {
        throw new Error("Invalid project ID");
      }

      const { startDate, endDate, comparisonStartDate, comparisonEndDate } =
        getFormattedDates();

      if (!startDate || !endDate) {
        throw new Error("Date range is required");
      }

      // Fetch current period data
      const { data: currentData } = await httpService.get(
        `/api/project/GA4/user/demographic/detailed-cities/${projectId}?start_date=${startDate}&end_date=${endDate}`,
        { useAuth: true }
      );

      let comparisonData: CitiesApiResponse | null = null;

      // Fetch comparison data if comparison is enabled and dates are available
      if (isComparisonEnabled && comparisonStartDate && comparisonEndDate) {
        try {
          const { data } = await httpService.get(
            `/api/project/GA4/user/demographic/detailed-cities/${projectId}?start_date=${comparisonStartDate}&end_date=${comparisonEndDate}`,
            { useAuth: true }
          );
          comparisonData = data;
        } catch (error) {
          console.warn("Failed to fetch comparison data:", error);
        }
      }

      // Create a map of comparison data for easy lookup
      const comparisonMap = new Map<string, CityData>();
      if (comparisonData?.data?.dimension_breakdown) {
        comparisonData.data.dimension_breakdown.forEach((city) => {
          comparisonMap.set(city.name, city);
        });
      }

      // Transform data for the table
      const tableHeadings = [
        "CITY",
        "TOTAL USERS",
        "NEW USERS",
        "SESSIONS",
        "ENGAGED SESSIONS",
        "VIEWS",
        "ENGAGEMENT RATE",
        "EVENT COUNT",
        "CONVERSIONS",
        "PERCENTAGE",
      ];

      // Transform all data (no pagination here)
      const allCitiesData = currentData.data.dimension_breakdown;

      const tableBody = allCitiesData.map((city: CityData) => {
        const comparisonCity = comparisonMap.get(city.name);

        return [
          { value: city.name },
          {
            value: formatNumber(city.total_users),
            growth: comparisonCity
              ? calculatePercentageChange(
                  city.total_users,
                  comparisonCity.total_users
                )
              : undefined,
          },
          {
            value: formatNumber(city.new_users),
            growth: comparisonCity
              ? calculatePercentageChange(
                  city.new_users,
                  comparisonCity.new_users
                )
              : undefined,
          },
          {
            value: formatNumber(city.sessions),
            growth: comparisonCity
              ? calculatePercentageChange(
                  city.sessions,
                  comparisonCity.sessions
                )
              : undefined,
          },
          {
            value: formatNumber(city.engaged_sessions),
            growth: comparisonCity
              ? calculatePercentageChange(
                  city.engaged_sessions,
                  comparisonCity.engaged_sessions
                )
              : undefined,
          },
          {
            value: formatNumber(city.views),
            growth: comparisonCity
              ? calculatePercentageChange(city.views, comparisonCity.views)
              : undefined,
          },
          {
            value: formatPercentage(city.engagement_rate),
            growth: comparisonCity
              ? calculatePercentageChange(
                  city.engagement_rate,
                  comparisonCity.engagement_rate
                )
              : undefined,
          },
          {
            value: formatNumber(city.event_count),
            growth: comparisonCity
              ? calculatePercentageChange(
                  city.event_count,
                  comparisonCity.event_count
                )
              : undefined,
          },
          {
            value: formatNumber(city.conversions),
            growth: comparisonCity
              ? calculatePercentageChange(
                  city.conversions,
                  comparisonCity.conversions
                )
              : undefined,
          },
          {
            value: `${city.percentage.toFixed(1)}%`,
            growth: comparisonCity
              ? calculatePercentageChange(
                  city.percentage,
                  comparisonCity.percentage
                )
              : undefined,
          },
        ];
      });

      // Calculate pagination
      const itemsPerPage = 5;
      const totalPages = Math.ceil(allCitiesData.length / itemsPerPage);

      return {
        tableHeadings,
        allTableBody: tableBody,
        totalPages,
        allData: allCitiesData,
        itemsPerPage,
      };
    },
    enabled: isValidProjectId && !!projectId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
};

// Main hook that handles pagination
const useCitiesData = ({ page }: { page: number }) => {
  const rawDataQuery = useCitiesDataRaw();

  // Handle pagination on the client side
  const paginatedData = React.useMemo(() => {
    if (!rawDataQuery.data) return null;

    const { allTableBody, tableHeadings, totalPages, itemsPerPage } =
      rawDataQuery.data;

    // Calculate pagination
    const startIndex = (page - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const paginatedTableBody = allTableBody.slice(startIndex, endIndex);

    return {
      tableData: {
        tableHeadings,
        tableBody: paginatedTableBody,
      },
      pagination: {
        totalPages,
        initialPage: page,
      },
    };
  }, [rawDataQuery.data, page]);

  return {
    ...rawDataQuery,
    data: paginatedData,
  };
};

export default useCitiesData;
