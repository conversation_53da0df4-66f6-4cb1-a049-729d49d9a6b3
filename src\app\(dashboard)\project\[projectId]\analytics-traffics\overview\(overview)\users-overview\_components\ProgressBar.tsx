import React from "react";

/* ============================== FRAMER MOTION ============================= */
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";
import Skeleton from "react-loading-skeleton";

const ProgressBar = ({
  percentage,
  className,
  title,
  color,
  isLoading,
}: {
  percentage: number;
  className?: string;
  title: string;
  color?: string;
  isLoading: boolean | false;
}) => {
  return (
    <div className={cn(className, "space-y-1.5 py-0.5")}>
      <div className="flex justify-between text-secondary text-xs font-medium">
        {isLoading ? (
          <Skeleton width={100} height={12} />
        ) : (
          <>
            <span className="truncate">{title}</span>
          </>
        )}
        {isLoading ? (
          <Skeleton width={30} height={12} />
        ) : (
          <span className="font-semibold text-primary">{percentage}%</span>
        )}
      </div>
      <div className="bg-gray-100 h-3 w-full overflow-hidden rounded-full">
        {isLoading ? (
          <Skeleton
            width={`${percentage}%`}
            height={12}
            className="rounded-full"
            style={{ borderRadius: "200px" }}
          />
        ) : (
          <motion.div
            className={`h-full rounded-full ${color || "bg-primary"}`}
            initial={{ width: 0 }}
            whileInView={{ width: `${percentage}%` }}
            transition={{ duration: 1.2, ease: "easeOut" }}
            viewport={{ once: true, amount: 0.2 }}
          />
        )}
      </div>
    </div>
  );
};

export default ProgressBar;
