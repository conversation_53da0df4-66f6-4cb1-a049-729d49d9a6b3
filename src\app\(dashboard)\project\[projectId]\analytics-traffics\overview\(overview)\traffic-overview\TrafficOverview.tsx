import React, { useState, useMemo } from "react";

/* ================================ SKELETON ================================ */
import Skeleton from "react-loading-skeleton";

/* =============================== COMPONENTS =============================== */
import Card from "@/components/ui/card";
import Title from "@/components/ui/Title";
import CardTab from "@/components/ui/card-tab/CardTab";
import OrganicTraffic from "./_components/OrganicTraffic";
import { Button } from "@/components/ui/button";
import DateRange from "../../../_components/date-range/DateRange";
import NoData from "../../../analytic-insight/_components/NoData";
import { useAppThemeColor } from "@/hooks/useSidebarThemeColor";

/* ================================== UTILS ================================= */
import {
  transformComparisonMetricToChartData,
  getMetricByName,
  formatMetricValue,
  getEmptyTrafficSources,
  generateTrafficOverviewTabs,
} from "./utils/transformTrafficData";

/* ================================== STORE ================================= */
import { useDateRangeStore } from "@/store/useDateRangeStore";

const TrafficOverViewSkeleton = () => {
  return (
    <div className="w-[90%] mx-8 p-2 space-y-2 border-l border-b">
      {Array.from({ length: 4 }).map((_, index) => (
        <div key={index}>
          <Skeleton width={80} height={15} />
          <Skeleton width={`${Math.floor(Math.random() * 101)}%`} />
        </div>
      ))}
    </div>
  );
};

/* ================================ API CALLS =============================== */
import useTrafficOverview from "./TrafficOverview.hook";
import Link from "next/link";
import type { TrafficOverviewResponse } from "../../../types/HorizontalBars.types";

/* ========================================================================== */
const TrafficOverview = () => {
  /* ================================ CONSTANTS =============================== */
  const { themeColor } = useAppThemeColor();

  // Use the real API hook
  const {
    data: apiResponse,
    isLoading: barsDataIsLoading,
    isPending: barsDataIsPending,
    error: barsDataError,
  } = useTrafficOverview();

  // Extract comparison data from API response
  const comparisonData = useMemo(() => {
    if (!apiResponse?.data) return null;
    return apiResponse.data;
  }, [apiResponse]);

  // For backward compatibility, create trafficData from current period
  const trafficData: TrafficOverviewResponse = useMemo(() => {
    if (!apiResponse || !comparisonData) {
      return {
        status: "loading",
        project_id: "",
        data: {
          period: { start_date: "", end_date: "", days_count: 0 },
          metrics: {
            total_users: {
              total_value: 0,
              traffic_sources: getEmptyTrafficSources(),
            },
            new_users: {
              total_value: 0,
              traffic_sources: getEmptyTrafficSources(),
            },
            sessions: {
              total_value: 0,
              traffic_sources: getEmptyTrafficSources(),
            },
            active_users: {
              total_value: 0,
              traffic_sources: getEmptyTrafficSources(),
            },
            page_views: {
              total_value: 0,
              traffic_sources: getEmptyTrafficSources(),
            },
            event_count: {
              total_value: 0,
              traffic_sources: getEmptyTrafficSources(),
            },
            conversions: {
              total_value: 0,
              traffic_sources: getEmptyTrafficSources(),
            },
          },
        },
        last_sync: "",
      };
    }

    return {
      status: apiResponse.status,
      project_id: apiResponse.project_id,
      data: comparisonData.current,
      last_sync: apiResponse.last_sync,
    };
  }, [apiResponse, comparisonData]);

  // Generate tabs from API data with comparison
  const trafficOverviewTabs = useMemo(() => {
    return generateTrafficOverviewTabs(comparisonData);
  }, [comparisonData]);

  const [activeTab, setActiveTab] = useState("Total Users");

  // Transform data for chart with comparison
  const chartData = useMemo(() => {
    if (!comparisonData?.current || !comparisonData?.previous) return null;

    const currentMetric = getMetricByName(comparisonData.current, activeTab);
    const previousMetric = getMetricByName(comparisonData.previous, activeTab);

    if (!currentMetric || !previousMetric) return null;

    return transformComparisonMetricToChartData(
      currentMetric,
      previousMetric,
      activeTab
    );
  }, [comparisonData, activeTab]);

  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */

  // Show error state
  if (barsDataError) {
    return (
      <Card className="space-y-4">
        <Title>Traffic Overview</Title>
        <DateRange />
        <div className="flex items-center justify-center h-32 text-red-500">
          <p>Error loading traffic data. Please try again later.</p>
        </div>
      </Card>
    );
  }

  // Show no data state
  if (
    !trafficData &&
    !barsDataIsLoading &&
    !barsDataIsPending &&
    !barsDataError
  ) {
    return (
      <Card className="space-y-4">
        <Title>Traffic Overview</Title>
        <DateRange />
        <NoData title="Traffic Overview" />
      </Card>
    );
  }

  return (
    <Card className="space-y-4">
      <Title>Traffic Overview</Title>
      <DateRange />

      <div className="flex justify-between overflow-x-auto gap-1 h-fit text-nowrap">
        {trafficOverviewTabs.map(({ id, title, value, changeValue }) => (
          <CardTab
            key={id}
            title={title}
            value={value}
            changeValue={changeValue}
            className={`border-2 `}
            style={
              activeTab === title
                ? { borderColor: themeColor }
                : { borderColor: "transparent" }
            }
            onSelect={() => setActiveTab(title)}
          />
        ))}
      </div>
      <div>
        {barsDataIsLoading || barsDataIsPending ? (
          <TrafficOverViewSkeleton />
        ) : (
          chartData && <OrganicTraffic barsData={chartData} />
        )}
      </div>
      <div className="w-full flex justify-end pt-9 pb-5">
        <Link
          href={"/project/analytics-traffics/analytic-insight?tab=traffics"}
        >
          <Button className="justify-self-end" variant={"default"}>
            See All Details
          </Button>
        </Link>
      </div>
    </Card>
  );
};

export default TrafficOverview;
