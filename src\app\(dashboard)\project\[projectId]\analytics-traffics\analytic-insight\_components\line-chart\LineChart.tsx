"use client";
import React, { useMemo, memo } from "react";

/* ================================ RECHARTS ================================ */
import {
  LineChart,
  Line,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  XAxis,
  YAxis,
} from "recharts";

/* ================================== TYPES ================================= */
import type { LineChartType } from "./LineChart.types";
type PayloadEntry = {
  dataKey: string;
  value: number | string;
  color: string;
};
type TooltipContentProps = {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  cardsData: any;
  payload?: PayloadEntry[];
  label?: string;
  tooltipItemsClassName?: string;
};

/* =============================== COMPONENTS =============================== */
import Card from "@/components/ui/card";
import abbreviateNumber from "@/utils/abbreviateNumber";
import Skeleton from "react-loading-skeleton";
import { cn } from "@/utils/cn";

/* ========================================================================== */
/**
 * Custom tooltip content for the Recharts LineChart.
 * Displays values with growth indicators and colored dots for each data line.
 *
 * @component
 * @param props - The props object.
 * @param props.payload - The array of data points currently hovered.
 * @param props.label - The label (usually the x-axis value) for the current tooltip.
 * @param props.cardsData - Metadata for each data key, including growth info.
 * @returns A styled card with tooltip content or null if no data.
 */
const TooltipContent = memo(
  ({
    payload,
    label,
    cardsData,
    tooltipItemsClassName,
  }: TooltipContentProps) => {
    if (!payload || !payload.length) return null;

    // Group primary and comparison data for better 2-column display
    const primaryData = payload.filter(
      (entry) => !entry.dataKey.includes("dotted_")
    );
    const comparisonData = payload.filter((entry) =>
      entry.dataKey.includes("dotted_")
    );
    const hasComparison = comparisonData.length > 0;

    console.log("Tooltip payload:", {
      primaryData,
      comparisonData,
      hasComparison,
    });

    return (
      <Card
        className="rounded-lg p-4 text-sm grid gap-3 border border-[#E0E0E0]"
        style={{ boxShadow: "0px 4px 8px 0px #3440541A" }}
      >
        <div className="text-sm font-semibold text-gray-900">{label}</div>

        {hasComparison ? (
          // Two column grid layout for comparison mode
          <div className="space-y-2">
            {primaryData.map((primaryEntry: PayloadEntry, index: number) => {
              // Find the corresponding comparison data
              const baseKey = primaryEntry.dataKey;
              const comparisonEntry = comparisonData.find(
                (comp) => comp.dataKey === `dotted_${baseKey}`
              );

              return (
                <div key={index} className="grid grid-cols-2 gap-4">
                  {/* Primary Data Column */}
                  <div className="flex items-center justify-between gap-2">
                    <div className="flex items-center gap-2">
                      <span
                        className="inline-block w-2 h-2 rounded-full"
                        style={{ backgroundColor: primaryEntry.color }}
                      />
                    </div>
                    <span className="font-bold text-xs">
                      {typeof primaryEntry.value === "string"
                        ? primaryEntry.value
                        : abbreviateNumber(primaryEntry.value)}
                    </span>
                  </div>

                  {/* Comparison Data Column */}
                  {comparisonEntry && (
                    <div className="flex items-center justify-between gap-2">
                      <div className="flex items-center gap-2">
                        <span
                          className="inline-block w-2 h-2 rounded-full border"
                          style={{
                            borderColor: comparisonEntry.color,
                            backgroundColor: "transparent",
                          }}
                        />
                      </div>
                      <span className="font-bold text-xs">
                        {typeof comparisonEntry.value === "string"
                          ? comparisonEntry.value
                          : abbreviateNumber(comparisonEntry.value)}
                      </span>
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        ) : (
          // Single column layout for primary data only
          <div className="space-y-2">
            {primaryData.map((entry: PayloadEntry, index: number) => (
              <div
                key={index}
                className="flex items-center justify-between gap-4"
              >
                <div className="flex items-center gap-2">
                  <span
                    className="inline-block w-2 h-2 rounded-full"
                    style={{ backgroundColor: entry.color }}
                  />
                </div>
                <div className="flex items-center gap-2">
                  <span className="font-bold">
                    {typeof entry.value === "string"
                      ? entry.value
                      : abbreviateNumber(entry.value)}
                  </span>
                </div>
              </div>
            ))}
          </div>
        )}
      </Card>
    );
  }
);
TooltipContent.displayName = "TooltipContent";

/* ========================================================================== */
/**
 * Renders a responsive multi-line chart using Recharts with custom tooltips.
 * It dynamically renders lines based on selected keys and supports "dotted_" lines for comparisons.
 *
 * @component
 * @param props - The props object.
 * @param props.lineChartData - Array of chart data points. Each item should include a "name" and any number of data series.
 * @param props.colors - An array of objects mapping each data key to a color.
 * @param props.selectedLines - List of active lines (keys) to display.
 * @param props.cardsData - Metadata for each data key used in tooltip.
 * @returns A fully responsive line chart.
 */

const GSCLineChart = memo(
  ({
    lineChartData,
    colors,
    selectedLines,
    cardsData,
    isLoading,
    className,
    tooltipItemsClassName,
  }: LineChartType) => {
    /* ========================================================================== */
    /*                                  constants                                 */
    /* ========================================================================== */

    const keys = useMemo(() => {
      const safeData = lineChartData ?? [];
      return safeData.length > 0
        ? Object.keys(safeData[0]).filter((k) => k !== "name")
        : [];
    }, [lineChartData]);
    const memoizedSelectedLines = useMemo(() => selectedLines, [selectedLines]);

    // Calculate dynamic Y-axis domain for better visibility of low values
    const yAxisDomain = useMemo(() => {
      if (!lineChartData || !memoizedSelectedLines?.length)
        return ["auto", "auto"];

      const activeKeys = keys.filter(
        (key) =>
          memoizedSelectedLines.includes(key) ||
          (key.includes("dotted_") &&
            memoizedSelectedLines.includes(key.replace("dotted_", "")))
      );

      if (activeKeys.length === 0) return ["auto", "auto"];

      let allValues: number[] = [];

      // Collect all values from active lines
      lineChartData.forEach((dataPoint) => {
        activeKeys.forEach((key) => {
          const value = dataPoint[key];
          if (typeof value === "number" && !isNaN(value)) {
            allValues.push(value);
          }
        });
      });

      if (allValues.length === 0) return ["auto", "auto"];

      const minValue = Math.min(...allValues);
      const maxValue = Math.max(...allValues);
      const range = maxValue - minValue;

      // Enhanced scaling logic for better visibility of low values

      // Case 1: All values are very small (under 50) - use tight scaling
      if (maxValue <= 50) {
        const padding = Math.max(0.5, range * 0.15);
        return [Math.max(0, minValue - padding), maxValue + padding];
      }

      // Case 2: Values are in the low-medium range (50-200) - use moderate scaling
      if (maxValue <= 200) {
        const padding = Math.max(1, range * 0.12);
        return [Math.max(0, minValue - padding), maxValue + padding];
      }

      // Case 3: Mixed small and large values - ensure small values are visible
      if (minValue < 100 && maxValue > 1000) {
        // Use logarithmic-inspired scaling to give more space to lower values
        const minPadding = Math.max(1, minValue * 0.1);
        const maxPadding = Math.max(10, maxValue * 0.05);
        return [Math.max(0, minValue - minPadding), maxValue + maxPadding];
      }

      // Case 4: Medium range values (200-1000) - standard scaling
      if (maxValue <= 1000) {
        const padding = Math.max(5, range * 0.08);
        return [Math.max(0, minValue - padding), maxValue + padding];
      }

      // Case 5: Large values - minimal padding to preserve scale
      const padding = Math.max(10, range * 0.05);
      return [Math.max(0, minValue - padding), maxValue + padding];
    }, [lineChartData, keys, memoizedSelectedLines]);

    // Custom tick formatter for Y-axis to handle different value ranges
    const formatYAxisTick = useMemo(() => {
      return (value: number) => {
        if (value === 0) return "0";
        if (value < 1) return value.toFixed(2);
        if (value < 10) return value.toFixed(1);
        if (value < 100) return Math.round(value).toString();
        if (value < 1000) return Math.round(value).toString();
        return abbreviateNumber(value);
      };
    }, []);

    // Dynamic height based on value ranges for better visibility
    const chartHeight = useMemo(() => {
      if (!lineChartData || !memoizedSelectedLines?.length) return "300px";

      const activeKeys = keys.filter(
        (key) =>
          memoizedSelectedLines.includes(key) ||
          (key.includes("dotted_") &&
            memoizedSelectedLines.includes(key.replace("dotted_", "")))
      );

      if (activeKeys.length === 0) return "300px";

      let allValues: number[] = [];
      lineChartData.forEach((dataPoint) => {
        activeKeys.forEach((key) => {
          const value = dataPoint[key];
          if (typeof value === "number" && !isNaN(value)) {
            allValues.push(value);
          }
        });
      });

      if (allValues.length === 0) return "300px";

      const maxValue = Math.max(...allValues);

      // Give more height for charts with smaller values for better visibility
      if (maxValue <= 50) return "350px";
      if (maxValue <= 200) return "320px";

      return "300px";
    }, [lineChartData, keys, memoizedSelectedLines]);

    /* ========================================================================== */
    /*                                   RENDER                                   */
    /* ========================================================================== */
    if (isLoading)
      return (
        <div className={cn(className)}>
          <Skeleton height={300} />
        </div>
      );

    if (!lineChartData || !colors || !selectedLines) return null;

    return (
      <div className={cn("w-full", className)} style={{ height: chartHeight }}>
        <ResponsiveContainer width="100%" height="100%">
          <LineChart
            width={500}
            height={300}
            data={lineChartData}
            margin={{
              top: 5,
              right: 30,
              left: 20,
              bottom: 5,
            }}
          >
            <XAxis
              interval="preserveStartEnd"
              dataKey="name"
              type="category"
              tickLine={false}
              axisLine={false}
              tick={{
                fill: "#6C757D",
                fontSize: 11,
                fontWeight: "bold",
              }}
              tickMargin={16}
              angle={-45}
              textAnchor="end"
              height={60}
            />
            <YAxis
              interval={0}
              type="number"
              domain={yAxisDomain}
              tickFormatter={formatYAxisTick}
              tickLine={false}
              axisLine={false}
              tick={{
                fill: "#6C757D",
                fontSize: 12,
                fontWeight: "bold",
              }}
              tickMargin={16}
            />
            <CartesianGrid stroke="#EAEAEA" />
            {cardsData && (
              <Tooltip
                content={
                  <TooltipContent
                    cardsData={cardsData}
                    tooltipItemsClassName={tooltipItemsClassName}
                  />
                }
              />
            )}
            {keys.map((key, index) => {
              const color = colors.find((col) => col.name === key);
              if (memoizedSelectedLines?.includes(key))
                if (!key.includes("dotted_"))
                  return (
                    <Line
                      key={index}
                      type="bump"
                      dataKey={key}
                      stroke={`${color?.color || "#ff00ff"}`}
                      strokeWidth={2}
                      strokeDasharray={index > colors.length - 1 ? "4 4" : "0"}
                      strokeLinecap="round"
                      activeDot={{ r: 4 }}
                    />
                  );
            })}
            {keys.map((key, index) => {
              const baseKey = key.replace("dotted_", "");
              const color = colors.find((col) => col.name === baseKey);
              // Render dotted lines when the base metric is selected and this is a dotted key
              if (
                key.includes("dotted_") &&
                memoizedSelectedLines?.includes(baseKey)
              )
                return (
                  <Line
                    key={`dotted-${index}`}
                    type="bump"
                    dataKey={key}
                    stroke={`${color?.color || "#ff00ff"}`}
                    strokeWidth={2}
                    strokeDasharray={"4 4"}
                    strokeLinecap="round"
                    activeDot={{ r: 4 }}
                  />
                );
            })}
          </LineChart>
        </ResponsiveContainer>
      </div>
    );
  }
);
GSCLineChart.displayName = "GSCLineChart";

export default GSCLineChart;
