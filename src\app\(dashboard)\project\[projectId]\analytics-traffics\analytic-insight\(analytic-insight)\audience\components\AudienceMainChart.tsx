"use client";
import React from "react";
import { motion } from "framer-motion";

/* =============================== COMPONENTS =============================== */
import GSCLineChart from "../../../_components/line-chart/LineChart";
import LineChartSkeleton from "../../../../_components/line-chart-skeleton/LineChartSkeleton";

/* ================================== TYPES ================================= */
import type { AudienceResponse } from "../Audience.types";

/* ========================================================================== */
interface AudienceMainChartProps {
  data: AudienceResponse | undefined;
  isLoading: boolean;
}

/* ========================================================================== */
const AudienceMainChart: React.FC<AudienceMainChartProps> = ({
  data,
  isLoading,
}) => {
  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */
  return (
    <div
      className="mt-8 min-h-[310px] overflow-hidden"
      style={{ position: "relative" }}
    >
      {isLoading ? (
        <motion.div
          key="loading"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.3 }}
        >
          <LineChartSkeleton />
        </motion.div>
      ) : data ? (
        <motion.div
          key="data"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.3 }}
        >
          <GSCLineChart
            lineChartData={data.lineChartData}
            colors={data.colors}
            selectedLines={data.selectedLines}
            cardsData={data.cardsData}
          />
        </motion.div>
      ) : null}
    </div>
  );
};

export default AudienceMainChart;
