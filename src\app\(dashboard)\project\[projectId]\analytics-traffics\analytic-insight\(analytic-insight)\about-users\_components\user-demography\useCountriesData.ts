import React from "react";
import { useQuery } from "@tanstack/react-query";
import httpService from "@/services/httpService";
import { useProjectId } from "@/hooks/useProjectId";
import { useDateRangeStore } from "@/store/useDateRangeStore";
import { TableDataRequest } from "../../../../../_components/data-table/DataTable.types";

// Types for the API response
interface CountryData {
  name: string;
  total_users: number;
  active_users: number;
  new_users: number;
  returning_users: number;
  sessions: number;
  engaged_sessions: number;
  views: number;
  avg_engagement_time: number;
  engagement_rate: number;
  event_count: number;
  conversions: number;
  percentage: number;
}

interface CountriesApiResponse {
  status: string;
  project_id: string;
  metric: string;
  data: {
    dimension_breakdown: CountryData[];
    daily_metrics: any[];
  };
}

// Helper function to calculate percentage change
const calculatePercentageChange = (
  current: number,
  previous: number
): string => {
  if (previous === 0) {
    return current > 0 ? "+100%" : "0%";
  }
  const change = ((current - previous) / previous) * 100;
  const sign = change >= 0 ? "+" : "";
  return `${sign}${change.toFixed(1)}%`;
};

// Helper function to format numbers
const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + "M";
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + "K";
  }
  return num.toString();
};

// Helper function to format engagement time
const formatEngagementTime = (seconds: number): string => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = Math.floor(seconds % 60);
  return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
};

// Helper function to format percentage
const formatPercentage = (rate: number): string => {
  return `${(rate * 100).toFixed(1)}%`;
};

// First, create a hook that fetches all the data
const useCountriesDataRaw = () => {
  const { projectId, isValidProjectId } = useProjectId();
  const { getFormattedDates, isComparisonEnabled } = useDateRangeStore();

  return useQuery({
    queryKey: ["countries-data", projectId, getFormattedDates()],
    queryFn: async () => {
      if (!isValidProjectId || !projectId) {
        throw new Error("Invalid project ID");
      }

      const { startDate, endDate, comparisonStartDate, comparisonEndDate } =
        getFormattedDates();

      if (!startDate || !endDate) {
        throw new Error("Date range is required");
      }

      // Fetch current period data
      const { data: currentData } = await httpService.get<CountriesApiResponse>(
        `/api/project/GA4/user/demographic/detailed-countries/${projectId}?start_date=${startDate}&end_date=${endDate}`,
        { useAuth: true }
      );

      let comparisonData: CountriesApiResponse | null = null;

      // Fetch comparison data if comparison is enabled and dates are available
      if (isComparisonEnabled && comparisonStartDate && comparisonEndDate) {
        try {
          const { data } = await httpService.get<CountriesApiResponse>(
            `/api/project/GA4/user/demographic/detailed-countries/${projectId}?start_date=${comparisonStartDate}&end_date=${comparisonEndDate}`,
            { useAuth: true }
          );
          comparisonData = data;
        } catch (error) {
          console.warn("Failed to fetch comparison data:", error);
        }
      }

      // Create a map of comparison data for easy lookup
      const comparisonMap = new Map<string, CountryData>();
      if (comparisonData?.data?.dimension_breakdown) {
        comparisonData.data.dimension_breakdown.forEach((country) => {
          comparisonMap.set(country.name, country);
        });
      }

      // Transform data for the table
      const tableHeadings = [
        "COUNTRY",
        "TOTAL USERS",
        "NEW USERS",
        "SESSIONS",
        "ENGAGED SESSIONS",
        "VIEWS",
        "ENGAGEMENT RATE",
        "EVENT COUNT",
        "CONVERSIONS",
        "PERCENTAGE",
      ];

      // Transform all data (no pagination here)
      const allCountriesData = currentData.data.dimension_breakdown;

      const tableBody = allCountriesData.map((country) => {
        const comparisonCountry = comparisonMap.get(country.name);

        return [
          { value: country.name },
          {
            value: formatNumber(country.total_users),
            growth: comparisonCountry
              ? calculatePercentageChange(
                  country.total_users,
                  comparisonCountry.total_users
                )
              : undefined,
          },
          {
            value: formatNumber(country.new_users),
            growth: comparisonCountry
              ? calculatePercentageChange(
                  country.new_users,
                  comparisonCountry.new_users
                )
              : undefined,
          },
          {
            value: formatNumber(country.sessions),
            growth: comparisonCountry
              ? calculatePercentageChange(
                  country.sessions,
                  comparisonCountry.sessions
                )
              : undefined,
          },
          {
            value: formatNumber(country.engaged_sessions),
            growth: comparisonCountry
              ? calculatePercentageChange(
                  country.engaged_sessions,
                  comparisonCountry.engaged_sessions
                )
              : undefined,
          },
          {
            value: formatNumber(country.views),
            growth: comparisonCountry
              ? calculatePercentageChange(
                  country.views,
                  comparisonCountry.views
                )
              : undefined,
          },
          {
            value: formatPercentage(country.engagement_rate),
            growth: comparisonCountry
              ? calculatePercentageChange(
                  country.engagement_rate,
                  comparisonCountry.engagement_rate
                )
              : undefined,
          },
          {
            value: formatNumber(country.event_count),
            growth: comparisonCountry
              ? calculatePercentageChange(
                  country.event_count,
                  comparisonCountry.event_count
                )
              : undefined,
          },
          {
            value: formatNumber(country.conversions),
            growth: comparisonCountry
              ? calculatePercentageChange(
                  country.conversions,
                  comparisonCountry.conversions
                )
              : undefined,
          },
          {
            value: `${country.percentage.toFixed(1)}%`,
            growth: comparisonCountry
              ? calculatePercentageChange(
                  country.percentage,
                  comparisonCountry.percentage
                )
              : undefined,
          },
        ];
      });

      // Calculate pagination
      const itemsPerPage = 5;
      const totalPages = Math.ceil(allCountriesData.length / itemsPerPage);

      return {
        tableHeadings,
        allTableBody: tableBody,
        totalPages,
        allData: allCountriesData,
        itemsPerPage,
      };
    },
    enabled: isValidProjectId && !!projectId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
};

// Main hook that handles pagination
const useCountriesData = ({ page }: { page: number }) => {
  const rawDataQuery = useCountriesDataRaw();

  // Handle pagination on the client side
  const paginatedData = React.useMemo(() => {
    if (!rawDataQuery.data) return null;

    const { allTableBody, tableHeadings, totalPages, itemsPerPage } =
      rawDataQuery.data;

    // Calculate pagination
    const startIndex = (page - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const paginatedTableBody = allTableBody.slice(startIndex, endIndex);

    return {
      tableData: {
        tableHeadings,
        tableBody: paginatedTableBody,
      },
      pagination: {
        totalPages,
        initialPage: page,
      },
    };
  }, [rawDataQuery.data, page]);

  return {
    ...rawDataQuery,
    data: paginatedData,
  };
};

export default useCountriesData;
