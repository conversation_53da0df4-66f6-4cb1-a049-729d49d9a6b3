import { useMemo } from "react";
import type { UsersOverviewData } from "./UsersOverview.type";
import useCountriesData from "./hooks/useCountriesData";
import useCitiesData from "./hooks/useCitiesData";
import useGenderData from "./hooks/useGenderData";
import useDeviceData from "./hooks/useDeviceData";
import useAgeData from "./hooks/useAgeData";
import { generateMockProgressBarData } from "./utils/dataTransform";

/**
 * Custom hook for fetching user overview data from GA4 API
 * @param userOverviewQuery - The selected tab (Countries, Cities, Gender, Device, Age)
 * @returns Query result with map data and progress bar data
 */
const useUsersOverview = (userOverviewQuery: string) => {
  const isSelectedTabCities = userOverviewQuery.toLowerCase() === "cities";
  const isSelectedTabGender = userOverviewQuery.toLowerCase() === "gender";
  const isSelectedTabDevice = userOverviewQuery.toLowerCase() === "device";
  const isSelectedTabAge = userOverviewQuery.toLowerCase() === "age";

  // Always fetch countries data for maps (independent of tab selection)
  const {
    data: countriesMapData,
    isLoading: isCountriesLoading,
    error: countriesError,
  } = useCountriesData();

  // Only fetch cities data when Cities tab is selected
  const {
    data: citiesProgressData,
    isLoading: isCitiesLoading,
    error: citiesError,
  } = useCitiesData(isSelectedTabCities);

  // Only fetch gender data when Gender tab is selected
  const {
    data: genderProgressData,
    isLoading: isGenderLoading,
    error: genderError,
  } = useGenderData(isSelectedTabGender);

  // Only fetch device data when Device tab is selected
  const {
    data: deviceProgressData,
    isLoading: isDeviceLoading,
    error: deviceError,
  } = useDeviceData(isSelectedTabDevice);

  // Only fetch age data when Age tab is selected
  const {
    data: ageProgressData,
    isLoading: isAgeLoading,
    error: ageError,
  } = useAgeData(isSelectedTabAge);

  // Determine the appropriate data based on selected tab
  const result = useMemo((): {
    data: UsersOverviewData | undefined;
    isLoading: boolean;
    error: Error | null;
  } => {
    const isSelectedTabCountries =
      userOverviewQuery.toLowerCase() === "countries";

    // Always use countries data for maps
    const leftMap = countriesMapData?.leftMap || {};
    const rightMap = countriesMapData?.rightMap || {};

    // Determine progress bar data based on selected tab
    let progressbarData: any[] = [];

    if (isSelectedTabCities) {
      // Use cities API data for Cities tab
      progressbarData = citiesProgressData || [];
    } else if (isSelectedTabGender) {
      // Use gender API data for Gender tab
      progressbarData = genderProgressData || [];
    } else if (isSelectedTabDevice) {
      // Use device API data for Device tab
      progressbarData = deviceProgressData || [];
    } else if (isSelectedTabAge) {
      // Use age API data for Age tab
      progressbarData = ageProgressData || [];
    } else if (isSelectedTabCountries && countriesMapData) {
      // Use countries API data for Countries tab progress bars
      progressbarData = countriesMapData.progressbarData || [];
    } else {
      // Fallback for any other tabs (should not happen)
      progressbarData = generateMockProgressBarData(userOverviewQuery);
    }

    // Determine overall loading state
    const isLoading =
      isCountriesLoading ||
      (isSelectedTabCities && isCitiesLoading) ||
      (isSelectedTabGender && isGenderLoading) ||
      (isSelectedTabDevice && isDeviceLoading) ||
      (isSelectedTabAge && isAgeLoading);

    // Determine overall error state
    const error =
      countriesError ||
      (isSelectedTabCities ? citiesError : null) ||
      (isSelectedTabGender ? genderError : null) ||
      (isSelectedTabDevice ? deviceError : null) ||
      (isSelectedTabAge ? ageError : null);

    // Return combined data
    const data: UsersOverviewData | undefined = countriesMapData
      ? {
          leftMap,
          rightMap,
          progressbarData,
        }
      : undefined;

    return {
      data,
      isLoading,
      error,
    };
  }, [
    userOverviewQuery,
    countriesMapData,
    citiesProgressData,
    genderProgressData,
    deviceProgressData,
    ageProgressData,
    isCountriesLoading,
    isCitiesLoading,
    isGenderLoading,
    isDeviceLoading,
    isAgeLoading,
    countriesError,
    citiesError,
    genderError,
    deviceError,
    ageError,
  ]);

  return result;
};

export default useUsersOverview;
