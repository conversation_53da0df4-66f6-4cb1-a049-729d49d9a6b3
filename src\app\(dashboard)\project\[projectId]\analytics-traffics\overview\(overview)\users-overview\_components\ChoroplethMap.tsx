import { useEffect, useId, useRef } from "react";
import ReactDOM from "react-dom/client";

/* =================================== D3 =================================== */
import * as d3 from "d3";
import type {
  Feature,
  GeoJsonProperties,
  Geometry,
  FeatureCollection,
} from "geojson";

/* ================================ CONSTANTS =============================== */
import { countryNames } from "@/constants/countryMapping";
import { cn } from "@/utils/cn";

/* ================================== TYPES ================================= */
type TooltipContentProps = Record<
  "name" | "value" | "users" | "percent",
  string
>;
type CountryValues = Record<string, string>;

interface ChoroplethMapProps {
  color: string;
  countryValues: CountryValues;
  className?: string;
}

/* ========================================================================== */
/*                                 COMPONENTS                                 */
/* ========================================================================== */
const TooltipContent = ({
  name,
  value,
  users,
  percent,
}: TooltipContentProps) => (
  <div className="text-secondary flex flex-col gap-1">
    <div className="text-xs space-x-1 font-bold">
      <span>{name}</span>
      {percent && <span className="text-primary-green">{percent}%</span>}
    </div>
    <span>{value || 0}%</span>
    <div className="flex whitespace-nowrap gap-1 text-secondary/50 font-bold">
      <span>{users || 0}</span>
      <span>users</span>
    </div>
  </div>
);

/* ========================================================================== */
const ChoroplethMap = ({
  color,
  countryValues,
  className,
}: ChoroplethMapProps) => {
  const tooltipId = useId();
  const svgRef = useRef<SVGSVGElement | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    function drawMap() {
      if (!svgRef.current || !containerRef.current) return;

      const { width, height } = containerRef.current.getBoundingClientRect();

      const svg = d3.select(svgRef.current);
      svg.attr("width", width).attr("height", height);
      svg.selectAll("*").remove();

      d3.json("/data/world.geo.json").then((data) => {
        const geoData = data as FeatureCollection<Geometry, GeoJsonProperties>;
        // Reserve space for legend at bottom (28px)
        const mapHeight = height - 28;
        const projection = d3
          .geoMercator()
          .scale(Math.min(width / 7, mapHeight / 5))
          // shift the map down by an extra 20px
          .translate([width / 2, mapHeight / 2 + 20]);

        const pathGenerator = d3.geoPath().projection(projection);

        const allValues = Object.values(countryValues)
          .map((raw) => Number(raw.split(",")[0]))
          .filter((val) => !isNaN(val));

        const maxValue = allValues.length ? Math.max(...allValues) : 100;
        const minValue = allValues.length ? Math.min(...allValues) : 0;

        const colorScale = d3
          .scalePow()
          .exponent(0.5)
          .domain([Math.max(0, minValue * 0.1), maxValue])
          .range([color === "#914AC4" ? "#E8D5F2" : "#FFF4CC", color])
          .clamp(true);

        svg
          .selectAll<SVGPathElement, Feature<Geometry, GeoJsonProperties>>(
            ".country"
          )
          .data(geoData.features)
          .join("path")
          .attr("class", "country")
          .attr("d", pathGenerator as any)
          .attr("fill", (d) => {
            const code = d.properties?.adm0_a3_br;
            const raw = code ? countryValues[code] : undefined;
            if (!raw) return "#F5F5F5";
            const scoreNum = Number(raw.split(",")[0]);
            return isNaN(scoreNum) ? "#F5F5F5" : colorScale(scoreNum);
          })
          .attr("stroke", "#fff")
          .attr("strokeWidth", 0.5)
          .on("mouseover", (event, d) => {
            const code = d.properties?.adm0_a3_br;
            const name = code ? countryNames[code] ?? code : "Unknown";
            const raw = code && countryValues[code] ? countryValues[code] : "";
            const [score = "", users = "", percent = ""] = raw
              .split(",")
              .map((s) => s.trim());

            const tooltipNode = document.getElementById(tooltipId);
            if (tooltipNode) {
              if (!(tooltipNode as any)._root) {
                (tooltipNode as any)._root = ReactDOM.createRoot(tooltipNode);
              }
              (tooltipNode as any)._root.render(
                <TooltipContent
                  name={name}
                  value={score}
                  users={users}
                  percent={percent}
                />
              );

              d3.select(tooltipNode).style("opacity", "1");
            }
          })
          .on("mousemove", (event) => {
            const [x, y] = d3.pointer(event, containerRef.current);
            d3.select(`#${tooltipId}`)
              .style("left", `${x + 10}px`)
              .style("top", `${y - 20}px`);
          })
          .on("mouseout", () => {
            d3.select(`#${tooltipId}`).style("opacity", "0");
          });
      });
    }

    drawMap();
    window.addEventListener("resize", drawMap);
    return () => window.removeEventListener("resize", drawMap);
  }, [color, countryValues, tooltipId]);

  return (
    <div
      ref={containerRef}
      className={cn(
        "relative w-full h-[320px] mt-4 lg:h-[420px] overflow-visible z-50",
        className
      )}
    >
      <div className="h-[calc(100%-28px)] w-full overflow-visible">
        <svg
          ref={svgRef}
          className="h-full w-full overflow-visible relative"
          preserveAspectRatio="xMidYMid meet"
        />
      </div>

      {/* Legend at bottom */}
      <div className="w-full -mt-12 flex justify-center">
        <div className="w-56 flex flex-col items-center">
          <div
            className="h-3 rounded-full w-full shadow-sm border"
            style={{
              background: `linear-gradient(90deg, ${
                color === "#914AC4"
                  ? "#F5EBFA 0%, #E8D5F2 25%, #D4B5E8 50%, #C195DE 75%, #914AC4 100%"
                  : "#FFFBF0 0%, #FFF4CC 25%, #FFEB99 50%, #FFE066 75%, #F8BD00 100%"
              })`,
              borderColor: color === "#914AC4" ? "#914AC4" : "#F8BD00",
            }}
          />
          <div className="flex text-[10px] text-secondary/70 justify-between w-full mt-1 font-medium">
            <span>Low</span>
            <span>High</span>
          </div>
        </div>
      </div>

      <div
        id={tooltipId}
        className="absolute pointer-events-none bg-white shadow-2xl text-secondary text-xs rounded px-2 py-1 opacity-0 transition-opacity duration-200 z-50"
      />
    </div>
  );
};

export default ChoroplethMap;
