"use client";
import React, { useEffect, useRef, useState } from "react";
import { motion } from "framer-motion";
import isEqual from "lodash.isequal";

/* =============================== COMPONENTS =============================== */
import CardTab from "@/components/ui/card-tab/CardTab";

/* ================================== TYPES ================================= */
import type { AudienceResponse } from "../Audience.types";
import type { CardTabType } from "@/app/(dashboard)/project/[projectId]/analytics-traffics/types/AnalyticsTraffics.types";
import { useProjectThemeColor } from "@/store/useProjectThemeColor";

/* ========================================================================== */
interface AudienceTabNavigationProps {
  data: AudienceResponse | undefined;
  isLoading: boolean;
  activeTab: string;
  onTabChange: (tab: string) => void;
}

/* ========================================================================== */
const AudienceTabNavigation: React.FC<AudienceTabNavigationProps> = ({
  data,
  isLoading,
  activeTab,
  onTabChange,
}) => {
  /* ========================================================================== */
  /*                                  CONSTANTS                                 */
  /* ========================================================================== */
  const themeColor = useProjectThemeColor((state) => state.themeColor);
  const prevCardTabsRef = useRef<CardTabType[] | null>(data?.cardTabs ?? null);
  const [cardsDataChanged, setCardsDataChanged] = useState(false);

  /* ========================================================================== */
  /*                                   EFFECTS                                  */
  /* ========================================================================== */
  useEffect(() => {
    if (data?.cardTabs && !isEqual(prevCardTabsRef.current, data.cardTabs)) {
      prevCardTabsRef.current = data.cardTabs;
      setCardsDataChanged(true);
    } else {
      setCardsDataChanged(false);
    }
  }, [data?.cardTabs]);

  useEffect(() => {
    if (data && activeTab === "") {
      onTabChange(data.cardTabs[0].title);
    }
  }, [data, activeTab, onTabChange]);

  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */
  return (
    <div className="flex overflow-x-auto max-w-full gap-1.5 px-1 pb-2 whitespace-nowrap min-h-[74px]">
      {cardsDataChanged
        ? Array.from({ length: 11 }).map((_, i) => (
            <CardTab key={i} isLoading />
          ))
        : prevCardTabsRef.current &&
          prevCardTabsRef.current.map(
            (
              {
                title,
                changeValue,
                value,
              }: { title: string; changeValue: string; value: string },
              index: number
            ) => (
              <CardTab
                key={index}
                title={title}
                value={value}
                changeValue={changeValue}
                className={`border-2 h-16 ${
                  activeTab === title ? "border-primary" : "border-transparent"
                }`}
                onSelect={() => onTabChange(title)}
              />
            )
          )}
    </div>
  );
};

export default AudienceTabNavigation;
