"use client";
import React, { useState, useEffect } from "react";

/* ================================== ICONS ================================= */
import { IoArrowBackCircleOutline } from "react-icons/io5";

/* ================================== TYPES ================================= */
type PaginationProps = {
  totalPages: number;
  page: number;
  onPageChange?: (page: number) => void;
};

/* ========================================================================== */
const Pagination = ({
  totalPages,
  page = 1,
  onPageChange,
}: PaginationProps) => {
  /* ========================================================================== */
  /*                                  CONSTANTS                                 */
  /* ========================================================================== */
  const [activePage, setActivePage] = useState(page);

  // Sync activePage with page prop changes
  useEffect(() => {
    setActivePage(page);
  }, [page]);

  if (!totalPages || totalPages < 2 || page <= 0) return null;

  /* ========================================================================== */
  /*                                  FUNCTIONS                                 */
  /* ========================================================================== */
  const handlePageClick = (page: number) => {
    if (page >= 1 && page <= totalPages) {
      setActivePage(page);
      onPageChange?.(page);
    }
  };

  const renderPageNumbers = () => {
    const visiblePages = 4;
    const pages = [];

    const start = Math.max(
      1,
      Math.min(
        activePage - Math.floor(visiblePages / 2),
        Math.max(1, totalPages - visiblePages + 1)
      )
    );

    for (let i = 0; i < visiblePages; i++) {
      const pageNum = start + i;
      if (pageNum > totalPages) break;
      pages.push(
        <span
          key={pageNum}
          onClick={() => handlePageClick(pageNum)}
          className={`${
            activePage === pageNum ? "text-secondary" : "text-secondary/40"
          } cursor-pointer px-1`}
        >
          {pageNum}
        </span>
      );
    }

    return pages;
  };

  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */
  return (
    <div className="w-full flex justify-center items-center text-secondary gap-4 text-sm">
      <IoArrowBackCircleOutline
        className={`h-5 w-5 cursor-pointer ${
          activePage === 1 ? "opacity-30 cursor-default" : ""
        }`}
        onClick={() => handlePageClick(activePage - 1)}
      />

      <div className="space-x-2 flex items-center">
        {renderPageNumbers()}
        {activePage < totalPages - 3 && (
          <>
            <span className="cursor-default">...</span>
            <span
              className="text-secondary/40 cursor-pointer"
              onClick={() => handlePageClick(totalPages)}
            >
              {totalPages}
            </span>
          </>
        )}
      </div>

      <IoArrowBackCircleOutline
        className={`scale-[-1] h-5 w-5 cursor-pointer ${
          activePage === totalPages ? "opacity-30 cursor-default" : ""
        }`}
        onClick={() => handlePageClick(activePage + 1)}
      />
    </div>
  );
};

export default Pagination;
