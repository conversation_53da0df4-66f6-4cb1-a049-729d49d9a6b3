import httpService from "@/services/httpService";
import { useQuery } from "@tanstack/react-query";
import { useProjectId } from "@/hooks/useProjectId";
import { useDateRangeStore } from "@/store/useDateRangeStore";
import {
  transformCountriesData,
  transformToProgressBarData,
  isValidCountriesResponse,
} from "../utils/dataTransform";
import type { CountryMapData } from "../UsersOverview.type";
import { ProgressbarData } from "../../../../types/AnalyticsTraffics.types";

// Extended type to include progress bar data for Countries tab
export interface CountriesDataResult extends CountryMapData {
  progressbarData: ProgressbarData[];
}

/**
 * Custom hook for fetching countries data from GA4 API (for map visualization)
 * This hook is called once and doesn't depend on tab selection
 * @returns Query result with map data only
 */
const useCountriesData = () => {
  const { projectId, isValidProjectId } = useProjectId();
  const { getFormattedDates, isComparisonEnabled } = useDateRangeStore();

  // Get formatted dates for API call (YYYY-MM-DD format)
  const { startDate, endDate, comparisonStartDate, comparisonEndDate } =
    getFormattedDates();

  return useQuery({
    queryKey: [
      "user-countries-data",
      projectId,
      startDate,
      endDate,
      comparisonStartDate,
      comparisonEndDate,
      isComparisonEnabled,
    ],
    queryFn: async (): Promise<CountriesDataResult> => {
      if (!projectId) {
        throw new Error("Project ID is required");
      }

      // Build API URL with GA4 endpoint format for user countries
      const apiUrl = `/api/project/GA4/user/overview/countries/${projectId}/`;

      try {
        // Fetch current period data (for top map)
        const currentParams: Record<string, string> = {};
        if (startDate && endDate) {
          currentParams.start_date = startDate;
          currentParams.end_date = endDate;
        }

        const currentResponse = await httpService.get(apiUrl, {
          params:
            Object.keys(currentParams).length > 0 ? currentParams : undefined,
          useAuth: true,
        });

        // Validate current response
        if (!isValidCountriesResponse(currentResponse.data)) {
          throw new Error("Invalid current period API response format");
        }

        let leftMap: Record<string, string> = {};
        let rightMap: Record<string, string> = {};

        // Transform current period data for top map (leftMap)
        const currentMapData = transformCountriesData(
          currentResponse.data,
          true
        );
        leftMap = currentMapData.leftMap;

        // If comparison is enabled and we have comparison dates, fetch comparison data
        if (isComparisonEnabled && comparisonStartDate && comparisonEndDate) {
          const comparisonParams: Record<string, string> = {
            start_date: comparisonStartDate,
            end_date: comparisonEndDate,
          };

          const comparisonResponse = await httpService.get(apiUrl, {
            params: comparisonParams,
            useAuth: true,
          });

          // Validate comparison response
          if (isValidCountriesResponse(comparisonResponse.data)) {
            // Transform comparison period data for bottom map (rightMap)
            const comparisonMapData = transformCountriesData(
              comparisonResponse.data,
              true
            );
            rightMap = comparisonMapData.leftMap; // Use leftMap data for rightMap display
          } else {
            console.warn(
              "Invalid comparison period API response, using empty data"
            );
          }
        } else {
          // If no comparison, use split data from current period
          const splitMapData = transformCountriesData(
            currentResponse.data,
            false
          );
          leftMap = splitMapData.leftMap;
          rightMap = splitMapData.rightMap;
        }

        // Generate progress bar data from current period data
        const progressbarData = transformToProgressBarData(
          currentResponse.data
        );

        return {
          leftMap,
          rightMap,
          progressbarData,
        };
      } catch (error) {
        console.error("Countries API call failed:", error);

        // Re-throw the error to let React Query handle it
        throw new Error(
          error instanceof Error
            ? `Failed to fetch user countries data: ${error.message}`
            : "Failed to fetch user countries data"
        );
      }
    },
    enabled: isValidProjectId && !!projectId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  });
};

export default useCountriesData;
