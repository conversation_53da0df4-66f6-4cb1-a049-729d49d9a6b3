"use client";
import React, { useRef, useState, useEffect } from "react";

/* ============================== FRAMER MOTION ============================= */
import { motion } from "framer-motion";

/* =============================== COMPONENTS =============================== */
import Pagination from "../../../../../_components/Pagination";
import DataTable from "@/app/(dashboard)/project/[projectId]/analytics-traffics/_components/data-table/DataTable";
import Card from "@/components/ui/card";
import useUserDemography from "./UserDemography.hooks";
import useCountriesData from "./useCountriesData";
import useCitiesData from "./useCitiesData";
import useGenderData from "./useGenderData";
import useLanguageData from "./useLanguageData";
import useAgeData from "./useAgeData";
import NoData from "../../../../_components/NoData";

/* ========================================================================== */
const TableSection = () => {
  /* ========================================================================== */
  /*                                  CONSTANTS                                 */
  /* ========================================================================== */
  const [page, setPage] = useState(1);
  const badges = ["Countries", "Cities", "Gender", "Language", "Age"];
  const [filterBy, setFilterBy] = useState(badges ? badges[0] : "");

  // Reset page to 1 when filter changes
  useEffect(() => {
    setPage(1);
  }, [filterBy]);

  // Use specific hooks based on the selected filter
  const {
    data: countriesData,
    isLoading: countriesLoading,
    isError: countriesError,
  } = useCountriesData({
    page,
  });

  const {
    data: citiesData,
    isLoading: citiesLoading,
    isError: citiesError,
  } = useCitiesData({
    page,
  });

  const {
    data: genderData,
    isLoading: genderLoading,
    isError: genderError,
  } = useGenderData({
    page,
  });

  const {
    data: languageData,
    isLoading: languageLoading,
    isError: languageError,
  } = useLanguageData({
    page,
  });

  const {
    data: ageData,
    isLoading: ageLoading,
    isError: ageError,
  } = useAgeData({
    page,
  });

  // Determine which data to use based on the filter
  const getDataByFilter = () => {
    switch (filterBy) {
      case "Countries":
        return {
          data: countriesData,
          isLoading: countriesLoading,
          isError: countriesError,
        };
      case "Cities":
        return {
          data: citiesData,
          isLoading: citiesLoading,
          isError: citiesError,
        };
      case "Gender":
        return {
          data: genderData,
          isLoading: genderLoading,
          isError: genderError,
        };
      case "Language":
        return {
          data: languageData,
          isLoading: languageLoading,
          isError: languageError,
        };
      case "Age":
        return {
          data: ageData,
          isLoading: ageLoading,
          isError: ageError,
        };
      default:
        return {
          data: countriesData,
          isLoading: countriesLoading,
          isError: countriesError,
        };
    }
  };

  const { data, isLoading, isError } = getDataByFilter();

  const lastDataRef = useRef(data);

  if (data) {
    lastDataRef.current = data;
  }

  const stableData = data ?? lastDataRef.current;

  // Reset page to 1 when filter changes
  useEffect(() => {
    setPage(1);
  }, [filterBy]);

  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */

  return isError ? (
    <motion.div
      key="error"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
    >
      <NoData title="All Reports-User's Demography" />
    </motion.div>
  ) : data?.tableData || isLoading ? (
    <Card className="w-full space-y-10 min-h-[520px] flex flex-col justify-between">
      <motion.div
        key="data"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
      >
        <DataTable
          title="All Reports-User's Demography"
          tableData={data?.tableData}
          isLoading={isLoading}
          badges={badges}
          selectedItem={filterBy}
          setSelectedItem={setFilterBy}
        />
      </motion.div>
      <Pagination
        totalPages={stableData?.pagination.totalPages || 1}
        page={page}
        onPageChange={setPage}
      />
    </Card>
  ) : (
    <motion.div
      key="nodata"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
    >
      <NoData title="All Reports-User's Demography" />
    </motion.div>
  );
};

export default TableSection;
