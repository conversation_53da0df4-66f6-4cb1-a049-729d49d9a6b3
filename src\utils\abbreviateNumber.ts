/**
 * Converts a number to a shortened string format with suffixes:
 * - B for billions
 * - M for millions
 * - K for thousands
 *
 * Examples:
 *   abbreviateNumber(1500)       // "1.5K"
 *   abbreviateNumber(2000000)    // "2M"
 *   abbreviateNumber(3500000000) // "3.5B"
 *   abbreviateNumber(999)        // "999"
 *
 * Trailing `.0` decimals are removed for cleaner output, e.g. `2.0M` → `2M`.
 *
 * @param num - The number to abbreviate
 * @returns A string representing the abbreviated number
 */
export default function abbreviateNumber(num: number): string {
  // Helper function to format with max 3 decimal places and remove trailing zeros
  const formatDecimal = (value: number, decimals: number = 1): string => {
    return parseFloat(value.toFixed(Math.min(decimals, 3))).toString();
  };

  if (num >= 1_000_000_000) return formatDecimal(num / 1_000_000_000) + "B";
  if (num >= 1_000_000) return formatDecimal(num / 1_000_000) + "M";
  if (num >= 1_000) return formatDecimal(num / 1_000) + "K";

  // For numbers less than 1000, format with max 3 decimal places
  return formatDecimal(num, 3);
}
