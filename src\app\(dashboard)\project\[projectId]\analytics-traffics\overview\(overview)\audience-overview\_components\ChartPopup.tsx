import Card from "@/components/ui/card";
import React from "react";

/* ================================== ICONS ================================= */
import { LuX } from "react-icons/lu";
import GSCLineChart from "../../../../analytic-insight/_components/line-chart/LineChart";

/* =============================== ANIMATIONS =============================== */
import { motion, AnimatePresence } from "framer-motion";

/* ================================= ZUSTAND ================================ */
import { useChartPopupStore } from "@/store/useChartPopupStore";
import { useLineChartDataStore } from "@/store/useChartPopupStore";

/* ================================== UTILS ================================= */
import { useMemo } from "react";

/* ========================================================================== */
const ChartPopup = () => {
  /* ========================================================================== */
  /*                                  CONSTANTS                                 */
  /* ========================================================================== */
  const { isVisible, hide } = useChartPopupStore();
  const { data, title, bigNumber, smallNumber, hasComparison } =
    useLineChartDataStore();

  // Helper function to format numbers with max 3 decimal places
  const formatNumber = (num: number): number => {
    return Math.round(num * 1000) / 1000;
  };

  // Transform data for GSCLineChart component
  // Uses separate keys (current_period, previous_period) to ensure both lines are solid
  // Avoids "dotted_" prefix which would make comparison line dotted
  const AUDIENCE_OVERVIEW_DATA = useMemo(() => {
    if (!data || data.length === 0) {
      return {
        cardsData: {
          current_period: { amount: bigNumber, growth: smallNumber },
        },
        colors: [{ name: "current_period", color: "#914AC4" }],
        lineChartData: [],
      };
    }

    // Check if we have comparison data
    const hasComparisonData = data.some(
      (item) => item.comparisonValue !== undefined
    );

    // Transform data for the line chart with separate keys for primary and comparison
    const transformedData = data.map((item) => ({
      name: item.name,
      current_period:
        typeof item.value === "number" ? formatNumber(item.value) : item.value,
      ...(hasComparisonData &&
        item.comparisonValue !== undefined && {
          previous_period: formatNumber(item.comparisonValue),
        }),
    }));

    // Define colors for primary and comparison lines (both solid)
    const colors = [
      { name: "current_period", color: "#914AC4" }, // Primary purple
      ...(hasComparisonData
        ? [{ name: "previous_period", color: "#FFCD29" }] // Comparison yellow (solid line)
        : []),
    ];

    // Cards data for tooltip
    const cardsData = {
      current_period: { amount: bigNumber, growth: smallNumber },
      ...(hasComparisonData && {
        previous_period: { amount: bigNumber, growth: smallNumber },
      }),
    };

    return {
      cardsData,
      colors,
      lineChartData: transformedData,
    };
  }, [data, bigNumber, smallNumber, hasComparison]);

  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */
  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          className="fixed top-0 left-0 w-screen h-screen z-30 flex justify-center items-center"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.2, ease: "easeOut" }}
        >
          {/* Backdrop */}
          <motion.div
            className="w-full h-full bg-white/35  absolute"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
            onClick={hide}
          />

          {/* Modal Content */}
          <motion.div
            className="absolute z-10"
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            transition={{ duration: 0.2, ease: "easeOut" }}
          >
            <Card className="bg-white w-full h-full lg:h-auto lg:w-[80vw] xl:w-[70vw] 2xl:w-[60vw] lg:max-w-[900px] flex flex-col justify-start lg:rounded-xl shadow-2xl border-1 border-gray-300 px-6 ">
              <div className="flex w-full justify-between items-center text-secondary ">
                <span className="text-lg font-bold text-secondary">
                  {title}
                </span>
                <motion.button
                  className="p-2 rounded-lg hover:bg-gray-100 transition-colors"
                  onClick={hide}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <LuX className="size-5 text-gray-500 hover:text-gray-700" />
                </motion.button>
              </div>
              <div className="space-y-4 mt-2">
                <div className="flex items-baseline space-x-1">
                  {bigNumber && (
                    <span className="text-2xl font-bold text-gray-900">
                      {bigNumber}
                    </span>
                  )}
                  {smallNumber && (
                    <span
                      className={`text-sm font-semiboldpy-1 rounded-md ${
                        smallNumber.includes("+")
                          ? "text-[#319F43] "
                          : smallNumber.includes("-")
                          ? "text-red-700 "
                          : "text-gray-700 "
                      }`}
                    >
                      {smallNumber}
                    </span>
                  )}
                </div>
                <div className="min-h-[400px]">
                  <GSCLineChart
                    lineChartData={AUDIENCE_OVERVIEW_DATA.lineChartData || []}
                    colors={AUDIENCE_OVERVIEW_DATA.colors}
                    selectedLines={AUDIENCE_OVERVIEW_DATA.colors.map(
                      (color) => color.name
                    )}
                    cardsData={AUDIENCE_OVERVIEW_DATA.cardsData}
                    className="h-[400px]"
                  />
                </div>
              </div>
            </Card>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default ChartPopup;
