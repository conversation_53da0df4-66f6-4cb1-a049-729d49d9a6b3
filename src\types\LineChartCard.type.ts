import { CurveType } from "recharts/types/shape/Curve";

export type LineChartDataItems = {
  name: string;
  value: number;
  comparisonValue?: number; // For dual period comparison
};

type LoadedData = {
  title?: string;
  className?: string;
  bigNumber?: string;
  smallNumber?: string;
  data: LineChartDataItems[];
  onClick?: () => void;
  isLoading?: false;
  chartType?: CurveType;
  hasComparison?: boolean;
};

type LoadingData = {
  title?: string;
  className?: string;
  bigNumber?: string;
  smallNumber?: string;
  data?: LineChartDataItems[];
  onClick?: () => void;
  isLoading: true;
  chartType?: CurveType;
  hasComparison?: boolean;
};
export type LineChartData = LoadingData | LoadedData;
