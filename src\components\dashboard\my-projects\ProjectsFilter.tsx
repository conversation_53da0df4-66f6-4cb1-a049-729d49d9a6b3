"use client";
import React, { useState } from "react";
import Dropdown from "@/components/shared/DropDown";
import { DownIcon } from "@/ui/icons/navigation";
import { ProjectsFilterProps } from "./types";

export default function ProjectsFilter({
  filter,
  visibleFilters,
  onFilterChange,
  closeModal,
}: ProjectsFilterProps) {
  const [isOpen, setIsOpen] = useState(false);

  const handleFilterChange = (query: string) => {
    onFilterChange(query);
    setIsOpen(false); // Close dropdown after selection
  };

  return (
    <div className="flex-shrink-0 mt-4 w-fit">
      <Dropdown
        onClose={closeModal}
        width={220} // Increased width to accommodate longer text
        align="start"
        className="hover:text-primary w-fit"
        open={isOpen}
        setOpen={setIsOpen}
        button={
          <button
            type="button"
            className="flex gap-2 items-center text-sm group px-3 py-2 rounded-lg border border-gray-200 hover:border-primary duration-200 w-[180px] justify-between"
            style={{ backgroundColor: "#F4F4F4" }}
          >
            <span
              className="font-medium group-hover:text-primary truncate"
              style={{ color: "#344054" }}
            >
              {filter.name}
            </span>
            <div
              className="transition-transform duration-200 group-data-[state=open]:rotate-180 flex-shrink-0"
              style={{ color: "#344054" }}
            >
              <DownIcon />
            </div>
          </button>
        }
      >
        <div className="p-1" style={{ backgroundColor: "#F4F4F4" }}>
          {visibleFilters.map((item, index) => {
            const isSelected = item.query === filter.query;
            return (
              <button
                type="button"
                key={item.query}
                className={`p-3 w-full text-left rounded-lg transition-all duration-200 flex items-center justify-between group ${
                  isSelected
                    ? "bg-primary/10 text-primary font-medium"
                    : "hover:bg-gray-50 hover:text-primary"
                }`}
                style={{
                  color: isSelected ? undefined : "#344054",
                }}
                onClick={() => handleFilterChange(item.query)}
              >
                <span>{item.name}</span>
                {isSelected && (
                  <svg
                    className="w-4 h-4 text-primary"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M5 13l4 4L19 7"
                    />
                  </svg>
                )}
              </button>
            );
          })}
        </div>
      </Dropdown>
    </div>
  );
}
