import httpService from "@/services/httpService";
import { useQuery } from "@tanstack/react-query";
import { useProjectId } from "@/hooks/useProjectId";
import { useDateRangeStore } from "@/store/useDateRangeStore";
import { ProgressbarData } from "../../../../types/AnalyticsTraffics.types";
import type { GenderAPIResponse } from "../UsersOverview.type";

/**
 * Check if Gender API response is valid
 * @param response - The Gender API response to validate
 * @returns Boolean indicating if response is valid
 */
function isValidGenderResponse(response: any): response is GenderAPIResponse {
  return (
    response &&
    typeof response === "object" &&
    response.status === "success" &&
    response.data &&
    Array.isArray(response.data.items) &&
    response.data.totals &&
    typeof response.data.totals.total_users === "number"
  );
}

/**
 * Transform Gender API response data to progress bar format
 * @param apiResponse - The Gender API response containing gender data
 * @returns Array of progress bar data
 */
function transformGenderToProgressBarData(
  apiResponse: GenderAPIResponse
): ProgressbarData[] {
  const { items } = apiResponse.data;

  // Sort by users count (descending) and take all items (usually just Male/Female)
  const sortedGenders = [...items].sort((a, b) => b.users - a.users);

  return sortedGenders.map((item) => ({
    title: item.name,
    percentage: Math.round(item.percentage),
  }));
}

/**
 * Custom hook for fetching gender data from GA4 API (for progress bars)
 * This hook is only called when the Gender tab is selected
 * @param enabled - Whether to enable the query (should be true only for Gender tab)
 * @returns Query result with gender progress bar data
 */
const useGenderData = (enabled: boolean = false) => {
  const { projectId, isValidProjectId } = useProjectId();
  const { getFormattedDates } = useDateRangeStore();

  // Get formatted dates for API call (YYYY-MM-DD format)
  const { startDate, endDate } = getFormattedDates();

  return useQuery({
    queryKey: [
      "user-gender-data",
      projectId,
      startDate,
      endDate,
    ],
    queryFn: async (): Promise<ProgressbarData[]> => {
      if (!projectId) {
        throw new Error("Project ID is required");
      }

      // Build API URL with GA4 endpoint format for user gender
      const apiUrl = `/api/project/GA4/user/overview/gender/${projectId}/`;

      try {
        // Fetch current period data
        const currentParams: Record<string, string> = {};
        if (startDate && endDate) {
          currentParams.start_date = startDate;
          currentParams.end_date = endDate;
        }

        const response = await httpService.get(apiUrl, {
          params:
            Object.keys(currentParams).length > 0 ? currentParams : undefined,
          useAuth: true,
        });

        // Validate response
        if (!isValidGenderResponse(response.data)) {
          throw new Error("Invalid gender API response format");
        }

        // Transform to progress bar data
        return transformGenderToProgressBarData(response.data);
      } catch (error) {
        console.error("Gender API call failed:", error);
        
        // Re-throw the error to let React Query handle it
        throw new Error(
          error instanceof Error
            ? `Failed to fetch user gender data: ${error.message}`
            : "Failed to fetch user gender data"
        );
      }
    },
    enabled: enabled && isValidProjectId && !!projectId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  });
};

export default useGenderData;
