import React, { forwardRef } from "react";

/* ================================== UTILS ================================= */
import { cn } from "@/utils/cn";

/* ============================== FRAMER MOTION ============================= */
import { motion } from "framer-motion";

/* ================================== TYPES ================================= */
import type { HTMLMotionProps } from "framer-motion";
type TCardProps = HTMLMotionProps<"div"> & {
  className?: string;
};

const Card = forwardRef<HTMLDivElement, TCardProps>(
  ({ children, className, style, ...props }, ref) => {
    return (
      <motion.div
        className={cn("bg-white p-4 rounded-lg", className)}
        style={style}
        ref={ref}
        {...props}
      >
        {children}
      </motion.div>
    );
  }
);

Card.displayName = "Card";

export default Card;
