import { useMemo } from "react";
import useAgeData from "../../../../../overview/(overview)/users-overview/hooks/useAgeData";
import useCountriesData from "../../../../../overview/(overview)/users-overview/hooks/useCountriesData";
import useCitiesData from "../../../../../overview/(overview)/users-overview/hooks/useCitiesData";

/**
 * Custom hook for ChartAndBars component to fetch data based on selected filter
 * @param activeTabFilter - The currently selected filter (Age, Countries, Cities, Language, Gender)
 * @returns Query result with progress bar data for the selected filter
 */
const useChartAndBarsData = (activeTabFilter: string) => {
  const isAgeFilterSelected = activeTabFilter.toLowerCase() === "age";
  const isCountriesFilterSelected =
    activeTabFilter.toLowerCase() === "countries";
  const isCitiesFilterSelected = activeTabFilter.toLowerCase() === "cities";

  // Only fetch age data when Age filter is selected
  const {
    data: ageProgressData,
    isLoading: isAgeLoading,
    error: ageError,
  } = useAgeData(isAgeFilterSelected);

  // Always fetch countries data (it's used for maps and Countries tab)
  const {
    data: countriesData,
    isLoading: isCountriesLoading,
    error: countriesError,
  } = useCountriesData();

  // Only fetch cities data when Cities filter is selected
  const {
    data: citiesProgressData,
    isLoading: isCitiesLoading,
    error: citiesError,
  } = useCitiesData(isCitiesFilterSelected);

  // Return the appropriate data based on selected filter
  const result = useMemo(() => {
    if (isAgeFilterSelected) {
      return {
        progressBarData: ageProgressData || [],
        isLoading: isAgeLoading,
        error: ageError,
        hasData: !!ageProgressData && ageProgressData.length > 0,
      };
    }

    if (isCountriesFilterSelected) {
      return {
        progressBarData: countriesData?.progressbarData || [],
        isLoading: isCountriesLoading,
        error: countriesError,
        hasData:
          !!countriesData?.progressbarData &&
          countriesData.progressbarData.length > 0,
      };
    }

    if (isCitiesFilterSelected) {
      return {
        progressBarData: citiesProgressData || [],
        isLoading: isCitiesLoading,
        error: citiesError,
        hasData: !!citiesProgressData && citiesProgressData.length > 0,
      };
    }

    // For other filters, return mock data (can be extended later for other API endpoints)
    const mockProgressBarData = [
      { title: "Sample Data 1", percentage: 25 },
      { title: "Sample Data 2", percentage: 35 },
      { title: "Sample Data 3", percentage: 20 },
      { title: "Sample Data 4", percentage: 15 },
      { title: "Sample Data 5", percentage: 5 },
    ];

    return {
      progressBarData: mockProgressBarData,
      isLoading: false,
      error: null,
      hasData: true,
    };
  }, [
    activeTabFilter,
    ageProgressData,
    isAgeLoading,
    ageError,
    isAgeFilterSelected,
    countriesData,
    isCountriesLoading,
    countriesError,
    isCountriesFilterSelected,
    citiesProgressData,
    isCitiesLoading,
    citiesError,
    isCitiesFilterSelected,
  ]);

  return result;
};

export default useChartAndBarsData;
