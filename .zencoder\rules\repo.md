---
description: Repository Information Overview
alwaysApply: true
---

# SEO Analyser App Information

## Summary

SEO Analyser is a Next.js web application for SEO analysis and project management. It provides tools for analyzing website SEO performance, tracking analytics, and managing SEO projects. The application features a dashboard interface with project management capabilities, analytics visualization, and user authentication.

## Structure

- **src/app**: Next.js application routes and API endpoints
- **src/components**: Reusable UI components organized by feature
- **src/services**: API service modules for backend communication
- **src/hooks**: Custom React hooks for shared functionality
- **src/store**: State management using Zustand
- **src/ui**: Base UI components and design system
- **src/utils**: Utility functions and helpers
- **src/tests**: Test files for components and utilities
- **public**: Static assets including images and fonts

## Language & Runtime

**Language**: TypeScript/JavaScript
**Version**: TypeScript 5.x, ES2017 target
**Framework**: Next.js 15.x, React 19.x
**Package Manager**: npm/bun (bun.lock present)

## Dependencies

**Main Dependencies**:

- **Next.js**: Full-stack React framework
- **React**: UI library (v19.0.0)
- **next-auth**: Authentication solution
- **zustand**: State management
- **@tanstack/react-query**: Data fetching and caching
- **axios**: HTTP client
- **tailwindcss**: Utility-first CSS framework
- **framer-motion**: Animation library
- **react-hook-form**: Form handling
- **apexcharts/recharts**: Data visualization
- **react-leaflet**: Map visualization

**Development Dependencies**:

- **TypeScript**: Static typing
- **eslint**: Code linting
- **tailwindcss**: CSS framework

## Build & Installation

```bash
# Install dependencies
npm install

# Development server
npm run dev

# Production build
npm run build

# Start production server
npm run start
```

## Server Configuration

The application includes a custom server.js file for HTTPS support in production:

- Uses Node.js HTTPS module with SSL certificates
- Redirects HTTP to HTTPS
- Serves the Next.js application on port 443 (HTTPS) and 80 (HTTP redirect)

## Testing

**Framework**: Jest
**Test Location**: src/tests/ and component-specific **tests** directories
**Naming Convention**: \*.test.js/tsx
**Run Command**: Not explicitly defined in package.json

## Project Features

- **Authentication**: User login/registration with next-auth
- **Project Management**: Create, edit, and manage SEO projects
- **Analytics**: Integration with analytics services
- **Dashboard**: Interactive dashboard with data visualization
- **Responsive Design**: Mobile and desktop support
- **White Labeling**: Support for white label functionality
- **Payment Integration**: Subscription and payment processing
